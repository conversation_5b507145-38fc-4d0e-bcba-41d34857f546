import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, Toplevel, filedialog
import os
import json
from PIL import Image, ImageTk
from database import DatabaseManager
from pokemon_utils import (
    parse_pokemon_filename, scan_pokemon_images, get_pokemon_v_count,
    format_pokemon_ivs, get_nature_display_name, parse_nature_from_display,
    validate_iv_value, calculate_breeding_compatibility
)
from ui_components import (
    ModernButton, ModernFrame, ModernLabelFrame, ToggleButton, ModernTreeview,
    ModernEntry, ModernCombobox, ModernSpinbox, CardFrame, StatusBar, apply_modern_theme,
    PokemonCard, FilterPanel, LoadingSpinner, GradientFrame, ModernTheme
)

class Pokemon:
    """宝可梦实例类，用于存储用户添加的具体宝可梦数据"""

    def __init__(self, instance_id=None, species_id=None, species_name="", pokedex_number=None,
                 nickname="", hp=0, attack=0, defense=0, sp_attack=0, sp_defense=0, speed=0,
                 nature="Hardy", gender="不明", price=0, is_shiny=False, is_locked=False, notes="", image_path=""):
        self.instance_id = instance_id  # 数据库中的实例ID
        self.species_id = species_id    # 宝可梦种类ID
        self.species_name = species_name  # 宝可梦种类名称
        self.pokedex_number = pokedex_number  # 图鉴编号
        self.nickname = nickname        # 昵称
        self.ivs = {
            "hp": hp,
            "attack": attack,
            "defense": defense,
            "sp_attack": sp_attack,
            "sp_defense": sp_defense,
            "speed": speed
        }
        self.nature = nature
        self.gender = gender
        self.price = price
        self.is_shiny = is_shiny
        self.is_locked = is_locked
        self.notes = notes
        self.image_path = image_path
        self.egg_groups = []  # 蛋组列表

    @property
    def display_name(self):
        """获取显示名称（昵称或种类名称）"""
        return self.nickname if self.nickname else self.species_name

    def get_v_count(self):
        """获取V的数量"""
        return get_pokemon_v_count(self.ivs)

    def to_dict(self):
        """将宝可梦转换为字典，用于保存"""
        return {
            "instance_id": self.instance_id,
            "species_id": self.species_id,
            "species_name": self.species_name,
            "pokedex_number": self.pokedex_number,
            "nickname": self.nickname,
            "ivs": self.ivs,
            "nature": self.nature,
            "gender": self.gender,
            "price": self.price,
            "is_shiny": self.is_shiny,
            "is_locked": self.is_locked,
            "notes": self.notes,
            "image_path": self.image_path,
            "egg_groups": self.egg_groups
        }

    @classmethod
    def from_dict(cls, data):
        """从字典创建宝可梦，用于加载"""
        pokemon = cls(
            instance_id=data.get("instance_id"),
            species_id=data.get("species_id"),
            species_name=data.get("species_name", data.get("name", "")),  # 兼容旧格式
            pokedex_number=data.get("pokedex_number"),
            nickname=data.get("nickname", ""),
            hp=data.get("ivs", {}).get("hp", 0),
            attack=data.get("ivs", {}).get("attack", 0),
            defense=data.get("ivs", {}).get("defense", 0),
            sp_attack=data.get("ivs", {}).get("sp_attack", 0),
            sp_defense=data.get("ivs", {}).get("sp_defense", 0),
            speed=data.get("ivs", {}).get("speed", 0),
            nature=data.get("nature", "Hardy"),
            gender=data.get("gender", "不明"),
            price=data.get("price", 0),
            is_shiny=data.get("is_shiny", False),
            is_locked=data.get("is_locked", False),
            notes=data.get("notes", ""),
            image_path=data.get("image_path", "")
        )
        pokemon.egg_groups = data.get("egg_groups", [])
        return pokemon

    @classmethod
    def from_database_row(cls, row):
        """从数据库行创建宝可梦实例"""
        pokemon = cls(
            instance_id=row.get("id"),
            species_id=row.get("pokemon_species_id"),
            species_name=row.get("species_name", ""),
            pokedex_number=row.get("pokedex_number"),
            nickname=row.get("nickname", ""),
            hp=row.get("hp_iv", 0),
            attack=row.get("attack_iv", 0),
            defense=row.get("defense_iv", 0),
            sp_attack=row.get("sp_attack_iv", 0),
            sp_defense=row.get("sp_defense_iv", 0),
            speed=row.get("speed_iv", 0),
            nature=row.get("nature", "Hardy"),
            gender=row.get("gender", "不明"),
            price=row.get("price", 0),
            is_shiny=row.get("is_shiny", False),
            is_locked=row.get("is_locked", False),
            notes=row.get("notes", ""),
            image_path=row.get("image_path", "")
        )
        return pokemon

class PokemmoBreedingCalculator:
    """宝可梦繁殖计算器主类"""

    def __init__(self, root):
        self.root = root
        self.root.title("POKEMMO 孵蛋计算器 v2.0")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)

        # 应用现代主题
        self.style = apply_modern_theme(self.root)

        # 存储图片引用
        self.image_refs = {}

        # 宝可梦列表
        self.pokemon_list = []
        self.filtered_pokemon = []
        self.pokemon_cards = []
        self.pokemon_species_list = []  # 宝可梦种类列表
        self.selected_pokemon = None

        # 初始化数据库
        self.db = DatabaseManager()
        self.init_database()

        # 创建主框架
        self.main_frame = ModernFrame(self.root, padding=20)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建标题栏
        self.create_header()

        # 创建标签页
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # 创建状态栏
        self.status_bar = StatusBar(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # 创建宝可梦管理页面
        self.create_pokemon_management_tab()

        # 创建繁殖计算页面
        self.create_breeding_calculator_tab()

        # 加载宝可梦数据
        self.load_pokemon_data()

        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 初始化变量
        self.selected_pokemon = None
        self.pokemon_cards = []
        self.filtered_pokemon = []

    def create_header(self):
        """创建简化的标题栏"""
        header_frame = tk.Frame(self.main_frame, bg="#3b82f6", height=60)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        header_frame.pack_propagate(False)

        title_label = tk.Label(
            header_frame,
            text="POKEMMO 孵蛋计算器 v2.0",
            font=("Arial", 16, "bold"),
            bg="#3b82f6",
            fg="white"
        )
        title_label.pack(pady=15)

        # 初始化状态标签（稍后会更新）
        self.db_status_label = None
        self.pokemon_count_label = None

    def init_database(self):
        """初始化数据库"""
        try:
            # 显示初始化进度
            self.show_loading_dialog("正在初始化数据库...")

            # 创建数据库（如果不存在）
            if not self.db.create_database_if_not_exists():
                self.hide_loading_dialog()
                self.show_database_setup_dialog()
                return False

            # 连接数据库
            if not self.db.connect():
                self.hide_loading_dialog()
                self.show_database_setup_dialog()
                return False

            # 创建表
            if not self.db.create_tables():
                self.hide_loading_dialog()
                messagebox.showerror("数据库错误", "无法创建数据库表")
                return False

            # 初始化蛋组数据
            if not self.db.initialize_egg_groups():
                self.hide_loading_dialog()
                messagebox.showerror("数据库错误", "无法初始化蛋组数据")
                return False

            # 扫描并导入宝可梦种类数据
            self.import_pokemon_species_from_images()

            self.hide_loading_dialog()
            return True

        except Exception as e:
            self.hide_loading_dialog()
            self.show_database_setup_dialog(str(e))
            return False

    def import_pokemon_species_from_images(self):
        """从images目录导入宝可梦种类数据"""
        try:
            pokemon_data = scan_pokemon_images()

            for data in pokemon_data:
                # 检查是否已存在
                existing = self.db.get_pokemon_species_by_name(data['name'])
                if not existing:
                    # 添加新的宝可梦种类
                    species_id = self.db.add_pokemon_species(
                        data['pokedex_number'] or 0,
                        data['name'],
                        data['image_path']
                    )

                    if species_id:
                        # 这里可以添加默认的蛋组信息
                        # 暂时先不添加，等用户手动设置
                        pass

        except Exception as e:
            print(f"导入宝可梦种类数据失败: {e}")
        
    def create_pokemon_management_tab(self):
        """创建宝可梦管理页面"""
        pokemon_tab = tk.Frame(self.notebook, bg="#f9fafb")
        self.notebook.add(pokemon_tab, text="🎮 宝可梦管理")

        # 创建主容器
        main_container = tk.Frame(pokemon_tab, bg="#f9fafb")
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 顶部筛选栏
        filter_bar = tk.Frame(main_container, bg="#ffffff", relief="solid", borderwidth=1)
        filter_bar.pack(fill=tk.X, pady=(0, 15))

        filter_content = tk.Frame(filter_bar, bg="#ffffff")
        filter_content.pack(fill=tk.X, padx=15, pady=10)

        # 搜索框
        search_frame = tk.Frame(filter_content, bg="#ffffff")
        search_frame.pack(side=tk.LEFT, padx=(0, 20))

        tk.Label(search_frame, text="搜索:", font=("Arial", 10), bg="#ffffff", fg="#374151").pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ModernEntry(search_frame, textvariable=self.search_var, placeholder="输入宝可梦名称...")
        search_entry.pack(side=tk.LEFT)
        search_entry.bind('<KeyRelease>', lambda e: self.apply_filters())

        # 蛋组筛选
        egg_group_frame = tk.Frame(filter_content, bg="#ffffff")
        egg_group_frame.pack(side=tk.LEFT, padx=(0, 20))

        tk.Label(egg_group_frame, text="蛋组:", font=("Arial", 10), bg="#ffffff", fg="#374151").pack(side=tk.LEFT, padx=(0, 5))
        self.egg_group_var = tk.StringVar(value="全部")
        self.egg_group_combo = ModernCombobox(egg_group_frame, textvariable=self.egg_group_var, values=["全部"], state="readonly", width=12)
        self.egg_group_combo.pack(side=tk.LEFT)
        self.egg_group_combo.bind('<<ComboboxSelected>>', lambda e: self.apply_filters())

        # V数筛选
        v_count_frame = tk.Frame(filter_content, bg="#ffffff")
        v_count_frame.pack(side=tk.LEFT, padx=(0, 20))

        tk.Label(v_count_frame, text="V数:", font=("Arial", 10), bg="#ffffff", fg="#374151").pack(side=tk.LEFT, padx=(0, 5))
        self.v_count_var = tk.StringVar(value="全部")
        v_count_combo = ModernCombobox(v_count_frame, textvariable=self.v_count_var, values=["全部", "0V", "1V", "2V", "3V", "4V", "5V", "6V"], state="readonly", width=8)
        v_count_combo.pack(side=tk.LEFT)
        v_count_combo.bind('<<ComboboxSelected>>', lambda e: self.apply_filters())

        # 状态筛选
        status_frame = tk.Frame(filter_content, bg="#ffffff")
        status_frame.pack(side=tk.LEFT, padx=(0, 20))

        self.show_locked_var = tk.BooleanVar(value=True)
        locked_check = tk.Checkbutton(status_frame, text="显示锁定", variable=self.show_locked_var, bg="#ffffff", command=self.apply_filters)
        locked_check.pack(side=tk.LEFT, padx=(0, 10))

        self.show_shiny_var = tk.BooleanVar(value=True)
        shiny_check = tk.Checkbutton(status_frame, text="显示闪光", variable=self.show_shiny_var, bg="#ffffff", command=self.apply_filters)
        shiny_check.pack(side=tk.LEFT)

        # 重置按钮
        reset_btn = ModernButton(filter_content, text="重置", command=self.reset_filters, style="secondary", size="small")
        reset_btn.pack(side=tk.RIGHT)

        # 主要内容区域 - 左右布局
        content_container = tk.Frame(main_container, bg="#f9fafb")
        content_container.pack(fill=tk.BOTH, expand=True)

        # 左侧宝可梦网格区域
        left_container = tk.Frame(content_container, bg="#f9fafb")
        left_container.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 15))

        # 右侧详情面板
        right_container = tk.Frame(content_container, bg="#f9fafb", width=300)
        right_container.pack(side=tk.RIGHT, fill=tk.Y)
        right_container.pack_propagate(False)

        # 网格标题和操作按钮
        grid_header = tk.Frame(left_container, bg="#f9fafb")
        grid_header.pack(fill=tk.X, pady=(0, 10))

        grid_title = tk.Label(
            grid_header,
            text="我的宝可梦",
            font=("Arial", 14, "bold"),
            bg="#f9fafb",
            fg="#1f2937"
        )
        grid_title.pack(side=tk.LEFT)

        # 操作按钮
        btn_frame = tk.Frame(grid_header, bg="#f9fafb")
        btn_frame.pack(side=tk.RIGHT)

        add_btn = ModernButton(btn_frame, text="+ 添加", command=self.show_add_pokemon_dialog, style="primary", size="small")
        add_btn.pack(side=tk.LEFT, padx=(0, 5))

        edit_btn = ModernButton(btn_frame, text="✏️ 编辑", command=self.edit_selected_pokemon, style="secondary", size="small")
        edit_btn.pack(side=tk.LEFT, padx=(0, 5))

        delete_btn = ModernButton(btn_frame, text="🗑️ 删除", command=self.delete_selected_pokemon, style="danger", size="small")
        delete_btn.pack(side=tk.LEFT)

        # 宝可梦网格容器
        grid_container = ModernFrame(left_container, padding=15, shadow=True)
        grid_container.pack(fill=tk.BOTH, expand=True)

        # 创建滚动区域
        self.create_pokemon_grid(grid_container)

        # 创建详情面板
        self.create_pokemon_detail_panel(right_container)

    def create_pokemon_detail_panel(self, parent):
        """创建宝可梦详情面板"""
        # 详情面板标题
        detail_title = tk.Label(
            parent,
            text="宝可梦详情",
            font=("Arial", 14, "bold"),
            bg="#f9fafb",
            fg="#1f2937"
        )
        detail_title.pack(pady=(0, 10))

        # 详情卡片
        self.detail_card = ModernFrame(parent, padding=15, shadow=True)
        self.detail_card.pack(fill=tk.BOTH, expand=True)

        # 默认提示
        self.detail_placeholder = tk.Label(
            self.detail_card,
            text="点击宝可梦卡片\n查看详细信息",
            font=("Arial", 12),
            bg="#ffffff",
            fg="#9ca3af",
            justify=tk.CENTER
        )
        self.detail_placeholder.pack(expand=True)

    def update_pokemon_details(self, pokemon):
        """更新宝可梦详情显示"""
        try:
            # 清空现有内容
            for widget in self.detail_card.winfo_children():
                widget.destroy()

            # 宝可梦图片
            image_frame = tk.Frame(self.detail_card, bg="#ffffff")
            image_frame.pack(fill=tk.X, pady=(0, 15))

            image_label = tk.Label(
                image_frame,
                text="🎮",
                font=("Arial", 48),
                bg="#ffffff",
                fg="#6b7280"
            )
            image_label.pack()

            # 基本信息
            info_frame = tk.Frame(self.detail_card, bg="#ffffff")
            info_frame.pack(fill=tk.X, pady=(0, 15))

            # 名称
            name = pokemon.nickname if pokemon.nickname else pokemon.species_name
            name_label = tk.Label(
                info_frame,
                text=name,
                font=("Arial", 16, "bold"),
                bg="#ffffff",
                fg="#1f2937"
            )
            name_label.pack()

            # 种类（如果有昵称）
            if pokemon.nickname:
                species_label = tk.Label(
                    info_frame,
                    text=f"({pokemon.species_name})",
                    font=("Arial", 12),
                    bg="#ffffff",
                    fg="#6b7280"
                )
                species_label.pack()

            # 个体值
            iv_frame = tk.Frame(self.detail_card, bg="#ffffff")
            iv_frame.pack(fill=tk.X, pady=(0, 15))

            iv_title = tk.Label(
                iv_frame,
                text="个体值",
                font=("Arial", 12, "bold"),
                bg="#ffffff",
                fg="#1f2937"
            )
            iv_title.pack(anchor="w")

            iv_grid = tk.Frame(iv_frame, bg="#ffffff")
            iv_grid.pack(fill=tk.X, pady=(5, 0))

            iv_attrs = [("HP", pokemon.ivs.get('hp', 0)), ("攻击", pokemon.ivs.get('attack', 0)),
                       ("防御", pokemon.ivs.get('defense', 0)), ("特攻", pokemon.ivs.get('sp_attack', 0)),
                       ("特防", pokemon.ivs.get('sp_defense', 0)), ("速度", pokemon.ivs.get('speed', 0))]

            for i, (attr, value) in enumerate(iv_attrs):
                row = i // 2
                col = i % 2

                attr_frame = tk.Frame(iv_grid, bg="#ffffff")
                attr_frame.grid(row=row, column=col, sticky="w", padx=(0, 20), pady=2)

                tk.Label(
                    attr_frame,
                    text=f"{attr}:",
                    font=("Arial", 10),
                    bg="#ffffff",
                    fg="#374151"
                ).pack(side=tk.LEFT)

                color = "#10b981" if value == 31 else "#6b7280"
                tk.Label(
                    attr_frame,
                    text=str(value),
                    font=("Arial", 10, "bold"),
                    bg="#ffffff",
                    fg=color
                ).pack(side=tk.LEFT, padx=(5, 0))

            # 其他信息
            other_frame = tk.Frame(self.detail_card, bg="#ffffff")
            other_frame.pack(fill=tk.X, pady=(0, 15))

            other_title = tk.Label(
                other_frame,
                text="其他信息",
                font=("Arial", 12, "bold"),
                bg="#ffffff",
                fg="#1f2937"
            )
            other_title.pack(anchor="w")

            # 性别、性格等
            info_items = [
                ("性别", pokemon.gender),
                ("性格", pokemon.nature),
                ("V数", f"{pokemon.get_v_count()}V"),
                ("价格", f"{pokemon.price:,}" if pokemon.price else "未设置")
            ]

            for label, value in info_items:
                item_frame = tk.Frame(other_frame, bg="#ffffff")
                item_frame.pack(fill=tk.X, pady=2)

                tk.Label(
                    item_frame,
                    text=f"{label}:",
                    font=("Arial", 10),
                    bg="#ffffff",
                    fg="#374151"
                ).pack(side=tk.LEFT)

                tk.Label(
                    item_frame,
                    text=str(value),
                    font=("Arial", 10, "bold"),
                    bg="#ffffff",
                    fg="#1f2937"
                ).pack(side=tk.LEFT, padx=(5, 0))

            # 状态标签
            status_frame = tk.Frame(self.detail_card, bg="#ffffff")
            status_frame.pack(fill=tk.X, pady=(0, 15))

            if hasattr(pokemon, 'is_locked') and pokemon.is_locked:
                lock_label = tk.Label(
                    status_frame,
                    text="🔒 已锁定",
                    font=("Arial", 10),
                    bg="#fef2f2",
                    fg="#dc2626",
                    padx=8,
                    pady=4
                )
                lock_label.pack(side=tk.LEFT, padx=(0, 5))

            if hasattr(pokemon, 'is_shiny') and pokemon.is_shiny:
                shiny_label = tk.Label(
                    status_frame,
                    text="✨ 闪光",
                    font=("Arial", 10),
                    bg="#fef3c7",
                    fg="#d97706",
                    padx=8,
                    pady=4
                )
                shiny_label.pack(side=tk.LEFT)

            # 操作按钮
            btn_frame = tk.Frame(self.detail_card, bg="#ffffff")
            btn_frame.pack(fill=tk.X, pady=(15, 0))

            # 锁定/解锁按钮
            lock_text = "🔓 解锁" if (hasattr(pokemon, 'is_locked') and pokemon.is_locked) else "🔒 锁定"
            lock_btn = ModernButton(
                btn_frame,
                text=lock_text,
                command=lambda: self.toggle_pokemon_lock(pokemon),
                style="warning" if (hasattr(pokemon, 'is_locked') and pokemon.is_locked) else "secondary",
                size="small"
            )
            lock_btn.pack(fill=tk.X, pady=(0, 5))

            # 编辑按钮
            edit_btn = ModernButton(
                btn_frame,
                text="✏️ 编辑",
                command=lambda: self.edit_selected_pokemon(),
                style="primary",
                size="small"
            )
            edit_btn.pack(fill=tk.X)

        except Exception as e:
            print(f"更新详情失败: {e}")

    def toggle_pokemon_lock(self, pokemon):
        """切换宝可梦锁定状态"""
        try:
            new_lock_status = not (hasattr(pokemon, 'is_locked') and pokemon.is_locked)

            # 更新数据库
            if self.db.update_pokemon_lock_status(pokemon.id, new_lock_status):
                # 更新本地对象
                pokemon.is_locked = new_lock_status

                # 刷新显示
                self.update_pokemon_details(pokemon)
                self.update_pokemon_grid()

                status_text = "锁定" if new_lock_status else "解锁"
                print(f"宝可梦 {pokemon.species_name} 已{status_text}")
            else:
                messagebox.showerror("错误", "更新锁定状态失败")

        except Exception as e:
            print(f"切换锁定状态失败: {e}")
            messagebox.showerror("错误", "更新锁定状态失败")

    def create_pokemon_grid(self, parent):
        """创建宝可梦网格"""
        # 创建画布和滚动条
        canvas = tk.Canvas(parent, bg="#ffffff", highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        self.pokemon_grid_frame = tk.Frame(canvas, bg="#ffffff")

        # 配置滚动
        self.pokemon_grid_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.pokemon_grid_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 布局
        canvas.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y")

        # 绑定鼠标滚轮
        def on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind("<MouseWheel>", on_mousewheel)

        self.pokemon_canvas = canvas

    def update_pokemon_grid(self):
        """更新宝可梦网格显示"""
        # 清除现有卡片
        for card in self.pokemon_cards:
            card.destroy()
        self.pokemon_cards.clear()

        # 应用筛选（如果筛选变量存在）
        if hasattr(self, 'search_var'):
            self.apply_filters()
        else:
            self.filtered_pokemon = self.pokemon_list.copy()

        # 创建新卡片
        cols = 5  # 每行显示5个
        for i, pokemon in enumerate(self.filtered_pokemon):
            row = i // cols
            col = i % cols

            card = PokemonCard(
                self.pokemon_grid_frame,
                pokemon,
                on_click=self.on_pokemon_card_click
            )
            card.grid(row=row, column=col, padx=5, pady=5, sticky="nw")

            # 加载图片
            if pokemon.image_path and os.path.exists(pokemon.image_path):
                card.load_image(pokemon.image_path)

            self.pokemon_cards.append(card)

        # 更新画布滚动区域
        self.pokemon_grid_frame.update_idletasks()
        self.pokemon_canvas.configure(scrollregion=self.pokemon_canvas.bbox("all"))

    def on_pokemon_card_click(self, pokemon):
        """宝可梦卡片点击事件"""
        # 取消之前选中的卡片
        for card in self.pokemon_cards:
            card.set_selected(False)

        # 选中当前卡片
        for card in self.pokemon_cards:
            if card.pokemon == pokemon:
                card.set_selected(True)
                break

        self.selected_pokemon = pokemon
        self.update_pokemon_details(pokemon)

    def on_filter_change(self, filters):
        """筛选条件改变"""
        self.update_pokemon_grid()

    def apply_filters(self):
        """应用筛选条件"""
        filters = self.filter_panel.get_filters()
        self.filtered_pokemon = []

        for pokemon in self.pokemon_list:
            # 搜索筛选
            if filters['search']:
                search_text = filters['search'].lower()
                if (search_text not in pokemon.species_name.lower() and
                    search_text not in (pokemon.nickname or "").lower()):
                    continue

            # 蛋组筛选
            if filters['egg_group'] != "全部":
                if filters['egg_group'] not in pokemon.egg_groups:
                    continue

            # V数筛选
            if filters['v_count'] != "全部":
                target_v = int(filters['v_count'][0])
                if pokemon.get_v_count() != target_v:
                    continue

            # 锁定状态筛选
            if not filters['show_locked'] and pokemon.is_locked:
                continue

            # 闪光状态筛选
            if not filters['show_shiny'] and pokemon.is_shiny:
                continue

            self.filtered_pokemon.append(pokemon)

    def toggle_pokemon_lock(self):
        """切换宝可梦锁定状态"""
        if not self.selected_pokemon:
            messagebox.showinfo("提示", "请先选择一个宝可梦")
            return

        # 切换锁定状态
        new_lock_state = not self.selected_pokemon.is_locked

        # 更新数据库
        pokemon_data = {
            'nickname': self.selected_pokemon.nickname,
            'gender': self.selected_pokemon.gender,
            'nature': self.selected_pokemon.nature,
            'hp_iv': self.selected_pokemon.ivs['hp'],
            'attack_iv': self.selected_pokemon.ivs['attack'],
            'defense_iv': self.selected_pokemon.ivs['defense'],
            'sp_attack_iv': self.selected_pokemon.ivs['sp_attack'],
            'sp_defense_iv': self.selected_pokemon.ivs['sp_defense'],
            'speed_iv': self.selected_pokemon.ivs['speed'],
            'price': self.selected_pokemon.price,
            'is_shiny': self.selected_pokemon.is_shiny,
            'is_locked': new_lock_state,
            'notes': self.selected_pokemon.notes
        }

        if self.db.update_pokemon_instance(self.selected_pokemon.instance_id, pokemon_data):
            self.selected_pokemon.is_locked = new_lock_state

            # 更新按钮文本
            if new_lock_state:
                self.lock_btn.configure(text="🔓 解锁")
                self.status_bar.set_status(f"已锁定 {self.selected_pokemon.display_name}", "#f59e0b")
            else:
                self.lock_btn.configure(text="🔒 锁定")
                self.status_bar.set_status(f"已解锁 {self.selected_pokemon.display_name}", "#10b981")

            # 刷新网格显示
            self.update_pokemon_grid()
        else:
            messagebox.showerror("错误", "更新锁定状态失败")

    def show_target_species_dialog(self):
        """显示目标宝可梦种类选择对话框"""
        # 创建对话框
        dialog = Toplevel(self.root)
        dialog.title("选择目标宝可梦种类")
        dialog.geometry("600x500")
        dialog.transient(self.root)
        dialog.grab_set()

        # 创建主框架
        main_frame = tk.Frame(dialog, bg="#ffffff", padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = tk.Label(
            main_frame,
            text="选择目标宝可梦种类",
            font=("Arial", 16, "bold"),
            bg="#ffffff",
            fg="#1f2937"
        )
        title_label.pack(pady=(0, 20))

        # 搜索框
        search_frame = tk.Frame(main_frame, bg="#ffffff")
        search_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(
            search_frame,
            text="搜索:",
            font=("Arial", 10),
            bg="#ffffff",
            fg="#374151"
        ).pack(side=tk.LEFT, padx=(0, 10))

        species_search_var = tk.StringVar()
        species_search_entry = ModernEntry(search_frame, textvariable=species_search_var)
        species_search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 种类列表
        list_frame = tk.Frame(main_frame, bg="#ffffff")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        columns = ("图鉴号", "名称")
        species_tree = ModernTreeview(list_frame, columns=columns, show="headings", height=15)

        species_tree.heading("图鉴号", text="图鉴号")
        species_tree.heading("名称", text="名称")
        species_tree.column("图鉴号", width=80)
        species_tree.column("名称", width=200)

        species_scroll = ttk.Scrollbar(list_frame, orient="vertical", command=species_tree.yview)
        species_tree.configure(yscrollcommand=species_scroll.set)
        species_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        species_tree.pack(fill=tk.BOTH, expand=True)

        # 加载数据
        def load_species_data(filter_text=""):
            for item in species_tree.get_children():
                species_tree.delete(item)

            if filter_text:
                species_list = self.db.search_pokemon_species(filter_text)
            else:
                species_list = self.db.get_all_pokemon_species()

            for species in species_list:
                species_tree.insert("", "end", values=(
                    species['pokedex_number'] if species['pokedex_number'] else "--",
                    species['name']
                ))

        species_search_var.trace("w", lambda *args: load_species_data(species_search_var.get()))
        load_species_data()

        # 按钮
        btn_frame = tk.Frame(main_frame, bg="#ffffff")
        btn_frame.pack(fill=tk.X)

        def select_species():
            selection = species_tree.selection()
            if not selection:
                messagebox.showinfo("提示", "请选择一个宝可梦种类")
                return

            item = species_tree.item(selection[0])
            species_name = item['values'][1]

            self.target_species_var.set(species_name)
            self.selected_target_species = self.db.get_pokemon_species_by_name(species_name)
            dialog.destroy()

        ModernButton(btn_frame, text="选择", command=select_species, style="primary").pack(side=tk.LEFT, padx=(0, 10))
        ModernButton(btn_frame, text="取消", command=dialog.destroy, style="secondary").pack(side=tk.LEFT)
        
        # 右侧详情面板
        detail_frame = tk.Frame(right_frame, bg="#ffffff", relief="solid", borderwidth=1)
        detail_frame.pack(fill=tk.BOTH, expand=True)

        # 详情标题
        detail_header = tk.Frame(detail_frame, bg="#f3f4f6", height=50)
        detail_header.pack(fill=tk.X)
        detail_header.pack_propagate(False)

        detail_title = tk.Label(
            detail_header,
            text="宝可梦详情",
            font=("Arial", 14, "bold"),
            bg="#f3f4f6",
            fg="#1f2937"
        )
        detail_title.pack(side=tk.LEFT, padx=15, pady=15)

        # 锁定按钮
        self.lock_btn = ModernButton(
            detail_header,
            text="🔒 锁定",
            command=self.toggle_pokemon_lock,
            style="warning"
        )
        self.lock_btn.pack(side=tk.RIGHT, padx=15, pady=10)

        # 宝可梦图片
        self.detail_image_label = ttk.Label(detail_frame)
        self.detail_image_label.pack(pady=10)

        # 宝可梦信息
        info_frame = ttk.Frame(detail_frame)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10)

        # 基本信息框架
        basic_frame = ttk.LabelFrame(info_frame, text="基本信息")
        basic_frame.pack(fill=tk.X, pady=5)

        # 图鉴编号
        pokedex_frame = ttk.Frame(basic_frame)
        pokedex_frame.pack(fill=tk.X, pady=2)

        ttk.Label(pokedex_frame, text="图鉴编号:", width=10).pack(side=tk.LEFT)
        self.detail_pokedex_label = ttk.Label(pokedex_frame, text="--")
        self.detail_pokedex_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 种类名称
        species_frame = ttk.Frame(basic_frame)
        species_frame.pack(fill=tk.X, pady=2)

        ttk.Label(species_frame, text="种类:", width=10).pack(side=tk.LEFT)
        self.detail_species_label = ttk.Label(species_frame, text="--")
        self.detail_species_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 昵称
        nickname_frame = ttk.Frame(basic_frame)
        nickname_frame.pack(fill=tk.X, pady=2)

        ttk.Label(nickname_frame, text="昵称:", width=10).pack(side=tk.LEFT)
        self.detail_nickname_label = ttk.Label(nickname_frame, text="--")
        self.detail_nickname_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 性别
        gender_frame = ttk.Frame(basic_frame)
        gender_frame.pack(fill=tk.X, pady=2)

        ttk.Label(gender_frame, text="性别:", width=10).pack(side=tk.LEFT)
        self.detail_gender_label = ttk.Label(gender_frame, text="--")
        self.detail_gender_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 性格
        nature_frame = ttk.Frame(basic_frame)
        nature_frame.pack(fill=tk.X, pady=2)

        ttk.Label(nature_frame, text="性格:", width=10).pack(side=tk.LEFT)
        self.detail_nature_label = ttk.Label(nature_frame, text="--")
        self.detail_nature_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 价格
        price_frame = ttk.Frame(basic_frame)
        price_frame.pack(fill=tk.X, pady=2)

        ttk.Label(price_frame, text="价格:", width=10).pack(side=tk.LEFT)
        self.detail_price_label = ttk.Label(price_frame, text="--")
        self.detail_price_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 蛋组信息
        egg_group_frame = ttk.LabelFrame(info_frame, text="蛋组")
        egg_group_frame.pack(fill=tk.X, pady=5)

        self.detail_egg_groups_label = ttk.Label(egg_group_frame, text="--", wraplength=200)
        self.detail_egg_groups_label.pack(fill=tk.X, padx=5, pady=5)

        # 个体值
        iv_frame = ttk.LabelFrame(info_frame, text="个体值")
        iv_frame.pack(fill=tk.X, pady=5)

        # 个体值属性
        iv_attrs = [("hp", "HP"), ("attack", "攻击"), ("defense", "防御"),
                    ("sp_attack", "特攻"), ("sp_defense", "特防"), ("speed", "速度")]

        self.detail_iv_labels = {}

        for i, (attr, label_text) in enumerate(iv_attrs):
            row = i // 3
            col = i % 3

            frame = ttk.Frame(iv_frame)
            frame.grid(row=row, column=col, padx=5, pady=2)

            ttk.Label(frame, text=f"{label_text}:").pack(side=tk.LEFT)
            iv_label = ttk.Label(frame, text="--")
            iv_label.pack(side=tk.LEFT, padx=5)

            self.detail_iv_labels[attr] = iv_label

        # 备注
        notes_frame = ttk.LabelFrame(info_frame, text="备注")
        notes_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.detail_notes_text = tk.Text(notes_frame, height=3, wrap=tk.WORD)
        self.detail_notes_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.detail_notes_text.config(state=tk.DISABLED)

    def create_breeding_calculator_tab(self):
        """创建繁殖计算页面"""
        breeding_tab = tk.Frame(self.notebook, bg="#f9fafb")
        self.notebook.add(breeding_tab, text="🥚 繁殖计算")

        # 创建主容器
        main_container = tk.Frame(breeding_tab, bg="#f9fafb")
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 创建左右分栏
        left_frame = tk.Frame(main_container, bg="#f9fafb")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        right_frame = tk.Frame(main_container, bg="#f9fafb")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))

        # 左侧 - 目标设置
        target_frame = ModernLabelFrame(left_frame, text="繁殖目标设置")
        target_frame.pack(fill=tk.X, pady=(0, 10))

        # 目标宝可梦选择
        species_frame = tk.Frame(target_frame, bg="#ffffff")
        species_frame.pack(fill=tk.X, padx=10, pady=10)

        species_label = tk.Label(
            species_frame,
            text="目标宝可梦:",
            font=("Arial", 12, "bold"),
            bg="#ffffff",
            fg="#1f2937"
        )
        species_label.pack(anchor="w", pady=(0, 5))

        species_select_frame = tk.Frame(species_frame, bg="#ffffff")
        species_select_frame.pack(fill=tk.X)

        self.target_species_var = tk.StringVar(value="请选择宝可梦种类")
        self.target_species_btn = ModernButton(
            species_select_frame,
            text="🔍 选择宝可梦种类",
            command=self.show_target_species_dialog,
            style="primary"
        )
        self.target_species_btn.pack(side=tk.LEFT)

        self.target_species_label = tk.Label(
            species_select_frame,
            textvariable=self.target_species_var,
            font=("Arial", 11),
            bg="#ffffff",
            fg="#374151"
        )
        self.target_species_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # 目标个体值
        iv_frame = ModernLabelFrame(target_frame, text="目标个体值")
        iv_frame.pack(fill=tk.X, pady=5)

        # 个体值属性
        iv_attrs = [("hp", "HP"), ("attack", "攻击"), ("defense", "防御"),
                   ("sp_attack", "特攻"), ("sp_defense", "特防"), ("speed", "速度")]

        self.target_iv_vars = {}
        self.target_iv_buttons = {}

        # 创建网格布局
        iv_grid_frame = tk.Frame(iv_frame, bg="#ffffff")
        iv_grid_frame.pack(fill=tk.X, padx=10, pady=10)

        for i, (attr, label_text) in enumerate(iv_attrs):
            row = i // 3
            col = i % 3

            # 创建个体值项框架
            item_frame = tk.Frame(iv_grid_frame, bg="#ffffff")
            item_frame.grid(row=row, column=col, padx=10, pady=5, sticky="w")

            # 标签
            label = tk.Label(
                item_frame,
                text=f"{label_text}:",
                font=("Arial", 10),
                bg="#ffffff",
                fg="#374151",
                width=6
            )
            label.pack(side=tk.LEFT)

            # 变量
            var = tk.IntVar(value=0)
            self.target_iv_vars[attr] = var

            # 切换按钮
            toggle_btn = ToggleButton(item_frame, var)
            toggle_btn.pack(side=tk.LEFT, padx=(5, 0))
            self.target_iv_buttons[attr] = toggle_btn
        
        # 目标性格
        nature_frame = ttk.Frame(target_frame)
        nature_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(nature_frame, text="目标性格:").pack(side=tk.LEFT, padx=5)
        
        self.target_nature_var = tk.StringVar(value="任意")
        natures = ["任意", "Hardy (中性)", "Lonely (孤独)", "Brave (勇敢)", "Adamant (固执)", "Naughty (顽皮)",
                  "Bold (大胆)", "Docile (坦率)", "Relaxed (悠闲)", "Impish (淘气)", "Lax (乐天)",
                  "Timid (胆小)", "Hasty (急躁)", "Serious (认真)", "Jolly (爽朗)", "Naive (天真)",
                  "Modest (内敛)", "Mild (温和)", "Quiet (冷静)", "Bashful (害羞)", "Rash (马虎)",
                  "Calm (温顺)", "Gentle (温和)", "Sassy (自大)", "Careful (慎重)", "Quirky (怪异)"]
        
        nature_combo = ttk.Combobox(
            nature_frame,
            values=natures,
            textvariable=self.target_nature_var,
            width=15
        )
        nature_combo.pack(side=tk.LEFT, padx=5)
        
        # 目标性别
        gender_frame = ttk.Frame(target_frame)
        gender_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(gender_frame, text="目标性别:").pack(side=tk.LEFT, padx=5)
        
        self.target_gender_var = tk.StringVar(value="不锁定")
        gender_combo = ttk.Combobox(
            gender_frame,
            values=["不锁定", "公", "母"],
            textvariable=self.target_gender_var,
            width=10
        )
        gender_combo.pack(side=tk.LEFT, padx=5)
        
        # 公母比例
        ttk.Label(gender_frame, text="公母比例:").pack(side=tk.LEFT, padx=(20, 5))
        
        self.ratio_var = tk.StringVar(value="1:1")
        ratio_combo = ttk.Combobox(
            gender_frame,
            values=["1:1", "1:3", "3:1", "7:1", "1:7"],
            textvariable=self.ratio_var,
            width=10
        )
        ratio_combo.pack(side=tk.LEFT, padx=5)
        
        # 素材选择
        materials_frame = ttk.LabelFrame(left_frame, text="繁殖素材")
        materials_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 添加说明
        ttk.Label(
            materials_frame, 
            text="选择要用于繁殖计算的宝可梦素材:",
            wraplength=300
        ).pack(pady=5)
        
        # 素材列表
        self.materials_var = tk.BooleanVar(value=True)  # 是否使用所有素材
        
        all_materials_check = ttk.Checkbutton(
            materials_frame,
            text="使用所有宝可梦素材",
            variable=self.materials_var,
            command=self.toggle_materials_selection
        )
        all_materials_check.pack(fill=tk.X, padx=5, pady=5)
        
        # 素材选择列表框
        self.materials_listbox = tk.Listbox(
            materials_frame,
            selectmode=tk.MULTIPLE,
            height=10
        )
        self.materials_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加滚动条
        materials_scroll = ttk.Scrollbar(self.materials_listbox, orient="vertical", command=self.materials_listbox.yview)
        self.materials_listbox.configure(yscrollcommand=materials_scroll.set)
        materials_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 右侧 - 计算结果
        result_frame = ttk.LabelFrame(right_frame, text="计算结果")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 结果显示区域
        self.result_text = tk.Text(result_frame, height=15, width=40)
        self.result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加滚动条
        result_scroll = ttk.Scrollbar(self.result_text, orient="vertical", command=self.result_text.yview)
        result_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.result_text.configure(yscrollcommand=result_scroll.set)
        
        # 最佳方案详情框架
        best_plan_frame = ttk.LabelFrame(right_frame, text="最佳繁殖方案")
        best_plan_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 方案详情显示
        self.plan_text = tk.Text(best_plan_frame, height=10, width=40)
        self.plan_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加滚动条
        plan_scroll = ttk.Scrollbar(self.plan_text, orient="vertical", command=self.plan_text.yview)
        plan_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.plan_text.configure(yscrollcommand=plan_scroll.set)
        
        # 计算按钮框架
        button_frame = ttk.Frame(right_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        # 计算按钮
        calculate_btn = ttk.Button(
            button_frame,
            text="计算最优繁殖方案",
            command=self.calculate_breeding_plan
        )
        calculate_btn.pack(pady=5)



    def update_pokemon_details(self, pokemon):
        """更新宝可梦详情显示"""
        # 更新锁定按钮
        if pokemon.is_locked:
            self.lock_btn.configure(text="🔓 解锁")
        else:
            self.lock_btn.configure(text="🔒 锁定")

        # 更新基本信息
        self.detail_pokedex_label.config(text=str(pokemon.pokedex_number) if pokemon.pokedex_number else "--")
        self.detail_species_label.config(text=pokemon.species_name)
        self.detail_nickname_label.config(text=pokemon.nickname if pokemon.nickname else "--")
        self.detail_gender_label.config(text=pokemon.gender)
        self.detail_nature_label.config(text=get_nature_display_name(pokemon.nature))
        self.detail_price_label.config(text=f"{pokemon.price:,}")

        # 更新蛋组信息
        egg_groups_text = ", ".join(pokemon.egg_groups) if pokemon.egg_groups else "--"
        self.detail_egg_groups_label.config(text=egg_groups_text)

        # 更新个体值
        for attr, label in self.detail_iv_labels.items():
            if attr in pokemon.ivs:
                iv_value = pokemon.ivs[attr]
                color = "#10b981" if iv_value == 31 else "#6b7280"
                label.config(text=str(iv_value), foreground=color)

        # 更新备注
        self.detail_notes_text.config(state=tk.NORMAL)
        self.detail_notes_text.delete(1.0, tk.END)
        if pokemon.notes:
            self.detail_notes_text.insert(1.0, pokemon.notes)
        self.detail_notes_text.config(state=tk.DISABLED)

        # 更新图片
        self.update_detail_image(pokemon)
    
    def update_detail_image(self, pokemon):
        """更新宝可梦详情图片"""
        # 清除当前图片
        self.detail_image_label.config(image="")

        # 获取图片路径
        image_path = pokemon.image_path
        if not image_path or not os.path.exists(image_path):
            # 尝试从images目录查找
            if pokemon.pokedex_number:
                # 尝试带图鉴编号的文件名
                image_path = os.path.join("images", f"{pokemon.pokedex_number:03d}{pokemon.species_name}.gif")
            if not os.path.exists(image_path):
                # 尝试只用名称的文件名
                image_path = os.path.join("images", f"{pokemon.species_name}.gif")

        # 如果找到图片，显示
        if os.path.exists(image_path):
            try:
                img = Image.open(image_path)
                img = img.resize((150, 150), Image.LANCZOS)  # 调整大小
                photo = ImageTk.PhotoImage(img)

                # 保存引用，防止被垃圾回收
                self.image_refs["detail"] = photo

                # 显示图片
                self.detail_image_label.config(image=photo)
            except Exception as e:
                print(f"图片加载错误: {str(e)}")
    
    def show_add_pokemon_dialog(self):
        """显示添加宝可梦对话框"""
        self.show_pokemon_species_selection_dialog()

    def show_pokemon_species_selection_dialog(self):
        """显示宝可梦种类选择对话框"""
        # 创建对话框
        dialog = Toplevel(self.root)
        dialog.title("选择宝可梦种类")
        dialog.geometry("600x500")
        dialog.transient(self.root)
        dialog.grab_set()

        # 创建主框架
        main_frame = ttk.Frame(dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 搜索框
        search_frame = ttk.Frame(main_frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(search_frame, text="搜索宝可梦:").pack(side=tk.LEFT, padx=(0, 5))
        species_search_var = tk.StringVar()
        species_search_entry = ttk.Entry(search_frame, textvariable=species_search_var)
        species_search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        # 种类列表
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 列表视图
        columns = ("图鉴号", "名称")
        species_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)

        # 设置列标题和宽度
        species_tree.heading("图鉴号", text="图鉴号")
        species_tree.heading("名称", text="名称")
        species_tree.column("图鉴号", width=80)
        species_tree.column("名称", width=200)

        # 添加滚动条
        species_scroll = ttk.Scrollbar(list_frame, orient="vertical", command=species_tree.yview)
        species_tree.configure(yscrollcommand=species_scroll.set)
        species_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        species_tree.pack(fill=tk.BOTH, expand=True)

        # 加载宝可梦种类数据
        def load_species_data(filter_text=""):
            # 清空列表
            for item in species_tree.get_children():
                species_tree.delete(item)

            # 获取种类数据
            if filter_text:
                species_list = self.db.search_pokemon_species(filter_text)
            else:
                species_list = self.db.get_all_pokemon_species()

            # 添加到列表
            for species in species_list:
                species_tree.insert("", "end", values=(
                    species['pokedex_number'] if species['pokedex_number'] else "--",
                    species['name']
                ))

        # 搜索事件
        def on_search_change(*args):
            load_species_data(species_search_var.get())

        species_search_var.trace("w", on_search_change)

        # 初始加载数据
        load_species_data()

        # 按钮框架
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X)

        # 选择按钮
        def select_species():
            selection = species_tree.selection()
            if not selection:
                messagebox.showinfo("提示", "请选择一个宝可梦种类")
                return

            # 获取选中的种类
            item = species_tree.item(selection[0])
            species_name = item['values'][1]

            # 获取种类详细信息
            species_data = self.db.get_pokemon_species_by_name(species_name)
            if species_data:
                dialog.destroy()
                self.show_pokemon_instance_dialog(species_data)

        select_btn = ttk.Button(btn_frame, text="选择", command=select_species)
        select_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 取消按钮
        cancel_btn = ttk.Button(btn_frame, text="取消", command=dialog.destroy)
        cancel_btn.pack(side=tk.LEFT)

    def edit_selected_pokemon(self):
        """编辑选中的宝可梦"""
        selection = self.pokemon_tree.selection()
        if not selection:
            messagebox.showinfo("提示", "请先选择一个宝可梦")
            return

        # 获取选中的宝可梦索引
        item_id = selection[0]
        item_index = self.pokemon_tree.index(item_id)

        # 确保索引有效
        if item_index < 0 or item_index >= len(self.pokemon_list):
            return

        # 获取选中的宝可梦
        pokemon = self.pokemon_list[item_index]

        # 获取种类信息
        species_data = self.db.get_pokemon_species_by_id(pokemon.species_id)
        if species_data:
            # 显示编辑对话框
            self.show_pokemon_instance_dialog(species_data, pokemon)

    def show_pokemon_instance_dialog(self, species_data, pokemon_instance=None):
        """显示宝可梦实例编辑对话框"""
        # 创建对话框
        dialog = Toplevel(self.root)
        dialog.title("编辑宝可梦" if pokemon_instance else "添加宝可梦")
        dialog.geometry("600x700")
        dialog.transient(self.root)
        dialog.grab_set()

        # 创建主框架
        main_frame = ttk.Frame(dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 种类信息显示
        species_frame = ttk.LabelFrame(main_frame, text="宝可梦种类")
        species_frame.pack(fill=tk.X, pady=(0, 10))

        species_info_frame = ttk.Frame(species_frame)
        species_info_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(species_info_frame, text=f"图鉴编号: {species_data['pokedex_number']}").pack(anchor=tk.W)
        ttk.Label(species_info_frame, text=f"名称: {species_data['name']}").pack(anchor=tk.W)

        # 获取并显示蛋组信息
        egg_groups = self.db.get_pokemon_egg_groups(species_data['id'])
        egg_groups_text = ", ".join([eg['name'] for eg in egg_groups]) if egg_groups else "未设置"
        ttk.Label(species_info_frame, text=f"蛋组: {egg_groups_text}").pack(anchor=tk.W)

        # 个体信息框架
        instance_frame = ttk.LabelFrame(main_frame, text="个体信息")
        instance_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 昵称
        nickname_frame = ttk.Frame(instance_frame)
        nickname_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(nickname_frame, text="昵称:", width=10).pack(side=tk.LEFT)
        nickname_var = tk.StringVar(value=pokemon_instance.nickname if pokemon_instance else "")
        nickname_entry = ttk.Entry(nickname_frame, textvariable=nickname_var)
        nickname_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

        # 性别
        gender_frame = ttk.Frame(instance_frame)
        gender_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(gender_frame, text="性别:", width=10).pack(side=tk.LEFT)
        gender_var = tk.StringVar(value=pokemon_instance.gender if pokemon_instance else "不明")
        gender_combo = ttk.Combobox(
            gender_frame,
            values=["公", "母", "不明"],
            textvariable=gender_var,
            width=15,
            state="readonly"
        )
        gender_combo.pack(side=tk.LEFT, padx=(5, 0))

        # 性格
        nature_frame = ttk.Frame(instance_frame)
        nature_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(nature_frame, text="性格:", width=10).pack(side=tk.LEFT)
        nature_var = tk.StringVar()
        if pokemon_instance:
            nature_var.set(get_nature_display_name(pokemon_instance.nature))
        else:
            nature_var.set("Hardy (中性)")

        natures = [
            "Hardy (中性)", "Lonely (孤独)", "Brave (勇敢)", "Adamant (固执)", "Naughty (顽皮)",
            "Bold (大胆)", "Docile (坦率)", "Relaxed (悠闲)", "Impish (淘气)", "Lax (乐天)",
            "Timid (胆小)", "Hasty (急躁)", "Serious (认真)", "Jolly (爽朗)", "Naive (天真)",
            "Modest (内敛)", "Mild (温和)", "Quiet (冷静)", "Bashful (害羞)", "Rash (马虎)",
            "Calm (温顺)", "Gentle (温和)", "Sassy (自大)", "Careful (慎重)", "Quirky (怪异)"
        ]
        nature_combo = ttk.Combobox(
            nature_frame,
            values=natures,
            textvariable=nature_var,
            width=20,
            state="readonly"
        )
        nature_combo.pack(side=tk.LEFT, padx=(5, 0))

        # 价格
        price_frame = ttk.Frame(instance_frame)
        price_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(price_frame, text="价格:", width=10).pack(side=tk.LEFT)
        price_var = tk.StringVar(value=str(pokemon_instance.price) if pokemon_instance else "0")
        price_entry = ttk.Entry(price_frame, textvariable=price_var)
        price_entry.pack(side=tk.LEFT, padx=(5, 0))

        # 是否闪光
        shiny_frame = ttk.Frame(instance_frame)
        shiny_frame.pack(fill=tk.X, padx=10, pady=5)

        shiny_var = tk.BooleanVar(value=pokemon_instance.is_shiny if pokemon_instance else False)
        shiny_check = ttk.Checkbutton(shiny_frame, text="闪光宝可梦", variable=shiny_var)
        shiny_check.pack(side=tk.LEFT)

        # 个体值
        iv_frame = ttk.LabelFrame(instance_frame, text="个体值")
        iv_frame.pack(fill=tk.X, padx=10, pady=10)

        # 个体值属性
        iv_attrs = [("hp", "HP"), ("attack", "攻击"), ("defense", "防御"),
                   ("sp_attack", "特攻"), ("sp_defense", "特防"), ("speed", "速度")]

        iv_vars = {}
        for i, (attr, label_text) in enumerate(iv_attrs):
            row = i // 3
            col = i % 3

            frame = ttk.Frame(iv_frame)
            frame.grid(row=row, column=col, padx=10, pady=5, sticky="w")

            ttk.Label(frame, text=f"{label_text}:", width=6).pack(side=tk.LEFT)
            value = pokemon_instance.ivs[attr] if pokemon_instance and attr in pokemon_instance.ivs else 0
            var = tk.IntVar(value=value)
            iv_vars[attr] = var

            spinbox = ttk.Spinbox(
                frame,
                from_=0,
                to=31,
                width=5,
                textvariable=var
            )
            spinbox.pack(side=tk.LEFT, padx=(5, 0))

            # 添加31按钮
            def set_31(attr=attr, var=var):
                var.set(31)

            btn_31 = ttk.Button(frame, text="31", width=3, command=set_31)
            btn_31.pack(side=tk.LEFT, padx=(2, 0))

        # 备注
        notes_frame = ttk.LabelFrame(instance_frame, text="备注")
        notes_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        notes_text = tk.Text(notes_frame, height=4, wrap=tk.WORD)
        notes_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        if pokemon_instance and pokemon_instance.notes:
            notes_text.insert(1.0, pokemon_instance.notes)

        # 按钮框架
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X, pady=(10, 0))

        # 保存按钮
        def save_pokemon():
            try:
                # 验证输入
                try:
                    price = int(price_var.get())
                except ValueError:
                    price = 0

                # 验证个体值
                for attr, var in iv_vars.items():
                    if not validate_iv_value(var.get()):
                        messagebox.showerror("输入错误", f"{attr}个体值必须在0-31之间")
                        return

                # 准备数据
                pokemon_data = {
                    'pokemon_species_id': species_data['id'],
                    'nickname': nickname_var.get().strip(),
                    'gender': gender_var.get(),
                    'nature': parse_nature_from_display(nature_var.get()),
                    'hp_iv': iv_vars['hp'].get(),
                    'attack_iv': iv_vars['attack'].get(),
                    'defense_iv': iv_vars['defense'].get(),
                    'sp_attack_iv': iv_vars['sp_attack'].get(),
                    'sp_defense_iv': iv_vars['sp_defense'].get(),
                    'speed_iv': iv_vars['speed'].get(),
                    'price': price,
                    'is_shiny': shiny_var.get(),
                    'notes': notes_text.get(1.0, tk.END).strip()
                }

                if pokemon_instance:
                    # 更新现有宝可梦
                    if self.db.update_pokemon_instance(pokemon_instance.instance_id, pokemon_data):
                        messagebox.showinfo("成功", "宝可梦信息已更新")
                        dialog.destroy()
                        self.load_pokemon_data()
                    else:
                        messagebox.showerror("错误", "更新宝可梦信息失败")
                else:
                    # 添加新宝可梦
                    instance_id = self.db.add_pokemon_instance(pokemon_data)
                    if instance_id:
                        messagebox.showinfo("成功", "宝可梦已添加")
                        dialog.destroy()
                        self.load_pokemon_data()
                    else:
                        messagebox.showerror("错误", "添加宝可梦失败")

            except Exception as e:
                messagebox.showerror("保存错误", f"保存宝可梦数据时出错: {str(e)}")

        save_btn = ttk.Button(btn_frame, text="保存", command=save_pokemon)
        save_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 取消按钮
        cancel_btn = ttk.Button(btn_frame, text="取消", command=dialog.destroy)
        cancel_btn.pack(side=tk.LEFT)

    def delete_selected_pokemon(self):
        """删除选中的宝可梦"""
        selection = self.pokemon_tree.selection()
        if not selection:
            messagebox.showinfo("提示", "请先选择一个宝可梦")
            return

        # 获取选中的宝可梦索引
        item_id = selection[0]
        item_index = self.pokemon_tree.index(item_id)

        # 确保索引有效
        if item_index < 0 or item_index >= len(self.pokemon_list):
            return

        # 获取选中的宝可梦
        pokemon = self.pokemon_list[item_index]

        # 确认删除
        if messagebox.askyesno("确认删除", f"确定要删除宝可梦 {pokemon.display_name} 吗?"):
            # 从数据库删除
            if pokemon.instance_id and self.db.delete_pokemon_instance(pokemon.instance_id):
                messagebox.showinfo("成功", "宝可梦已删除")
                self.load_pokemon_data()
            else:
                messagebox.showerror("错误", "删除宝可梦失败")
    
    def show_pokemon_dialog(self, pokemon=None, index=None):
        """显示宝可梦添加/编辑对话框"""
        # 创建对话框
        dialog = Toplevel(self.root)
        dialog.title("添加宝可梦" if pokemon is None else "编辑宝可梦")
        dialog.geometry("500x600")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 创建主框架
        main_frame = ttk.Frame(dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建表单
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 名称
        name_frame = ttk.Frame(form_frame)
        name_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(name_frame, text="名称:", width=10).pack(side=tk.LEFT)
        name_var = tk.StringVar(value=pokemon.name if pokemon else "")
        name_entry = ttk.Entry(name_frame, textvariable=name_var)
        name_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 性别
        gender_frame = ttk.Frame(form_frame)
        gender_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(gender_frame, text="性别:", width=10).pack(side=tk.LEFT)
        gender_var = tk.StringVar(value=pokemon.gender if pokemon else "不明")
        gender_combo = ttk.Combobox(
            gender_frame,
            values=["公", "母", "不明"],
            textvariable=gender_var,
            width=15
        )
        gender_combo.pack(side=tk.LEFT)
        
        # 性格
        nature_frame = ttk.Frame(form_frame)
        nature_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(nature_frame, text="性格:", width=10).pack(side=tk.LEFT)
        nature_var = tk.StringVar(value=pokemon.nature if pokemon else "Hardy")
        natures = ["Hardy (中性)", "Lonely (孤独)", "Brave (勇敢)", "Adamant (固执)", "Naughty (顽皮)",
                  "Bold (大胆)", "Docile (坦率)", "Relaxed (悠闲)", "Impish (淘气)", "Lax (乐天)",
                  "Timid (胆小)", "Hasty (急躁)", "Serious (认真)", "Jolly (爽朗)", "Naive (天真)",
                  "Modest (内敛)", "Mild (温和)", "Quiet (冷静)", "Bashful (害羞)", "Rash (马虎)",
                  "Calm (温顺)", "Gentle (温和)", "Sassy (自大)", "Careful (慎重)", "Quirky (怪异)"]
        nature_combo = ttk.Combobox(
            nature_frame,
            values=natures,
            textvariable=nature_var,
            width=15
        )
        nature_combo.pack(side=tk.LEFT)
        
        # 价格
        price_frame = ttk.Frame(form_frame)
        price_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(price_frame, text="价格:", width=10).pack(side=tk.LEFT)
        price_var = tk.StringVar(value=str(pokemon.price) if pokemon else "0")
        price_entry = ttk.Entry(price_frame, textvariable=price_var)
        price_entry.pack(side=tk.LEFT)
        
        # 个体值
        iv_frame = ttk.LabelFrame(form_frame, text="个体值")
        iv_frame.pack(fill=tk.X, pady=10)
        
        # 个体值属性
        iv_attrs = [("hp", "HP"), ("attack", "攻击"), ("defense", "防御"), 
                   ("sp_attack", "特攻"), ("sp_defense", "特防"), ("speed", "速度")]
        
        iv_vars = {}
        for i, (attr, label_text) in enumerate(iv_attrs):
            row = i // 3
            col = i % 3
            
            frame = ttk.Frame(iv_frame)
            frame.grid(row=row, column=col, padx=5, pady=2)
            
            ttk.Label(frame, text=f"{label_text}:").pack(side=tk.LEFT)
            value = pokemon.ivs[attr] if pokemon and attr in pokemon.ivs else 0
            var = tk.IntVar(value=value)
            iv_vars[attr] = var
            
            spinbox = ttk.Spinbox(
                frame,
                from_=0,
                to=31,
                width=5,
                textvariable=var
            )
            spinbox.pack(side=tk.LEFT, padx=5)
        
        # 图片选择
        image_frame = ttk.LabelFrame(form_frame, text="宝可梦图片")
        image_frame.pack(fill=tk.X, pady=10)
        
        # 图片显示
        image_label = ttk.Label(image_frame)
        image_label.pack(pady=10)
        
        # 图片路径
        image_path_var = tk.StringVar(value=pokemon.image_path if pokemon else "")
        
        # 如果有图片路径，显示图片
        if pokemon and pokemon.image_path and os.path.exists(pokemon.image_path):
            try:
                img = Image.open(pokemon.image_path)
                img = img.resize((100, 100), Image.LANCZOS)
                photo = ImageTk.PhotoImage(img)
                dialog.photo = photo  # 保存引用
                image_label.config(image=photo)
            except Exception:
                pass
        
        # 选择图片按钮
        def select_image():
            path = filedialog.askopenfilename(
                title="选择宝可梦图片",
                filetypes=[("GIF files", "*.gif"), ("All files", "*.*")]
            )
            if path:
                image_path_var.set(path)
                try:
                    img = Image.open(path)
                    img = img.resize((100, 100), Image.LANCZOS)
                    photo = ImageTk.PhotoImage(img)
                    dialog.photo = photo  # 保存引用
                    image_label.config(image=photo)
                except Exception as e:
                    messagebox.showerror("图片加载错误", f"无法加载图片: {str(e)}")
        
        select_btn = ttk.Button(
            image_frame,
            text="选择图片",
            command=select_image
        )
        select_btn.pack(pady=5)
        
        # 按钮框架
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X, pady=10)
        
        # 保存按钮
        def save_pokemon():
            try:
                # 获取输入的数据
                name = name_var.get().strip()
                if not name:
                    messagebox.showerror("输入错误", "宝可梦名称不能为空")
                    return
                    
                # 创建或更新宝可梦对象
                if pokemon is None:
                    new_pokemon = Pokemon()
                else:
                    new_pokemon = pokemon
                    
                new_pokemon.name = name
                new_pokemon.gender = gender_var.get()
                
                # 从性格字符串中提取纯性格名称
                nature = nature_var.get()
                if " (" in nature:
                    nature = nature.split(" (")[0]
                new_pokemon.nature = nature
                
                # 价格
                try:
                    new_pokemon.price = int(price_var.get())
                except ValueError:
                    new_pokemon.price = 0
                    
                # 个体值
                for attr, var in iv_vars.items():
                    new_pokemon.ivs[attr] = var.get()
                    
                # 图片路径
                new_pokemon.image_path = image_path_var.get()
                
                # 如果是添加新的宝可梦
                if pokemon is None:
                    self.pokemon_list.append(new_pokemon)
                
                # 更新显示和保存数据
                self.update_pokemon_list()
                self.save_pokemon_data()
                
                # 关闭对话框
                dialog.destroy()
                
            except Exception as e:
                messagebox.showerror("保存错误", f"保存宝可梦数据时出错: {str(e)}")
        
        save_btn = ttk.Button(
            btn_frame,
            text="保存",
            command=save_pokemon
        )
        save_btn.pack(side=tk.LEFT, padx=5)
        
        # 取消按钮
        cancel_btn = ttk.Button(
            btn_frame,
            text="取消",
            command=dialog.destroy
        )
        cancel_btn.pack(side=tk.LEFT, padx=5)
    

    

    
    def save_pokemon_data(self):
        """保存宝可梦数据（现在使用数据库，此方法保留用于兼容性）"""
        # 数据现在直接保存到数据库，不需要额外的保存操作
        pass
    
    def toggle_materials_selection(self):
        """切换素材选择状态"""
        if self.materials_var.get():
            # 禁用列表框
            self.materials_listbox.configure(state=tk.DISABLED)
        else:
            # 启用列表框
            self.materials_listbox.configure(state=tk.NORMAL)
    
    def update_materials_list(self):
        """更新素材列表"""
        # 清空列表框
        self.materials_listbox.delete(0, tk.END)

        # 添加宝可梦到列表框
        for pokemon in self.pokemon_list:
            display_name = pokemon.display_name
            self.materials_listbox.insert(tk.END, f"{display_name} ({pokemon.get_v_count()}V)")
    
    def load_pokemon_data(self):
        """从数据库加载宝可梦数据"""
        try:
            self.status_bar.set_status("正在加载宝可梦数据...", "#3b82f6")

            # 清空当前列表
            self.pokemon_list = []

            # 从数据库加载宝可梦实例
            instances = self.db.get_pokemon_instances()

            for instance_data in instances:
                pokemon = Pokemon.from_database_row(instance_data)
                self.pokemon_list.append(pokemon)

            # 更新网格显示
            self.update_pokemon_grid()

            # 更新素材列表
            self.update_materials_list()

            # 更新状态显示
            self.update_status_display()

            # 更新筛选面板的蛋组选项
            self.update_filter_egg_groups()

            self.status_bar.set_status("数据加载完成", "#10b981")

        except Exception as e:
            self.status_bar.set_status("数据加载失败", "#ef4444")
            messagebox.showerror("加载错误", f"加载宝可梦数据时出错: {str(e)}")

    def update_status_display(self):
        """更新状态显示"""
        try:
            # 如果状态标签不存在，跳过更新
            if self.db_status_label is None or self.pokemon_count_label is None:
                return

            # 更新数据库状态
            if self.db.connection:
                self.db_status_label.configure(text="数据库: 已连接")
            else:
                self.db_status_label.configure(text="数据库: 未连接")

            # 更新宝可梦数量
            count = len(self.pokemon_list)
            self.pokemon_count_label.configure(text=f"宝可梦: {count} 个")

        except Exception as e:
            print(f"更新状态显示失败: {e}")

    def update_filter_egg_groups(self):
        """更新筛选面板的蛋组选项"""
        try:
            if hasattr(self, 'egg_group_combo'):
                egg_groups = self.db.get_all_egg_groups()
                egg_group_names = ["全部"] + [group['name'] for group in egg_groups]
                self.egg_group_combo['values'] = egg_group_names
        except Exception as e:
            print(f"更新蛋组选项失败: {e}")

    def apply_filters(self):
        """应用筛选条件"""
        try:
            # 如果筛选变量不存在，直接返回所有宝可梦
            if not hasattr(self, 'search_var'):
                self.filtered_pokemon = self.pokemon_list.copy()
                return

            # 获取筛选条件
            search_text = self.search_var.get().lower()
            egg_group = self.egg_group_var.get()
            v_count = self.v_count_var.get()
            show_locked = self.show_locked_var.get()
            show_shiny = self.show_shiny_var.get()

            # 筛选宝可梦列表
            self.filtered_pokemon = []
            for pokemon in self.pokemon_list:
                # 文本搜索
                if search_text:
                    name_match = search_text in pokemon.species_name.lower()
                    nickname_match = pokemon.nickname and search_text in pokemon.nickname.lower()
                    if not (name_match or nickname_match):
                        continue

                # 蛋组筛选
                if egg_group != "全部":
                    if not hasattr(pokemon, 'egg_groups') or egg_group not in pokemon.egg_groups:
                        continue

                # V数筛选
                if v_count != "全部":
                    target_v = int(v_count[0])  # 提取数字
                    if pokemon.get_v_count() != target_v:
                        continue

                # 状态筛选
                if not show_locked and hasattr(pokemon, 'is_locked') and pokemon.is_locked:
                    continue
                if not show_shiny and hasattr(pokemon, 'is_shiny') and pokemon.is_shiny:
                    continue

                self.filtered_pokemon.append(pokemon)

        except Exception as e:
            print(f"应用筛选失败: {e}")
            self.filtered_pokemon = self.pokemon_list.copy()

    def reset_filters(self):
        """重置所有筛选条件"""
        try:
            self.search_var.set("")
            self.egg_group_var.set("全部")
            self.v_count_var.set("全部")
            self.show_locked_var.set(True)
            self.show_shiny_var.set(True)
            self.apply_filters()
        except Exception as e:
            print(f"重置筛选失败: {e}")



    def on_pokemon_card_click(self, pokemon):
        """宝可梦卡片点击事件"""
        try:
            # 取消所有卡片的选中状态
            for widget in self.pokemon_grid_frame.winfo_children():
                if isinstance(widget, PokemonCard):
                    widget.set_selected(False)

            # 设置当前选中的宝可梦
            self.selected_pokemon = pokemon

            # 找到对应的卡片并设置为选中
            for widget in self.pokemon_grid_frame.winfo_children():
                if isinstance(widget, PokemonCard) and widget.pokemon == pokemon:
                    widget.set_selected(True)
                    break

            # 更新详情面板
            self.update_pokemon_details(pokemon)

        except Exception as e:
            print(f"处理卡片点击失败: {e}")

    def show_loading_dialog(self, message="加载中..."):
        """显示现代化加载对话框"""
        theme = ModernTheme()

        self.loading_dialog = Toplevel(self.root)
        self.loading_dialog.title("请稍候")
        self.loading_dialog.geometry("350x200")
        self.loading_dialog.transient(self.root)
        self.loading_dialog.grab_set()
        self.loading_dialog.configure(bg=theme.COLORS["bg_primary"])

        # 居中显示
        self.loading_dialog.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 100,
            self.root.winfo_rooty() + 100
        ))

        # 创建现代化内容
        main_frame = ModernFrame(self.loading_dialog, padding=40, shadow=True)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 加载动画
        self.loading_spinner = LoadingSpinner(
            main_frame,
            size=48,
            color=theme.COLORS["primary"]
        )
        self.loading_spinner.pack(pady=(0, 20))
        self.loading_spinner.start_spinning()

        # 加载文字
        self.loading_label = tk.Label(
            main_frame,
            text=message,
            font=theme.FONTS["heading"],
            bg=theme.COLORS["bg_primary"],
            fg=theme.COLORS["text_primary"]
        )
        self.loading_label.pack()

        # 进度提示
        progress_label = tk.Label(
            main_frame,
            text="请稍候，正在处理您的请求...",
            font=theme.FONTS["small"],
            bg=theme.COLORS["bg_primary"],
            fg=theme.COLORS["text_secondary"]
        )
        progress_label.pack(pady=(10, 0))

        # 更新显示
        self.loading_dialog.update()

    def hide_loading_dialog(self):
        """隐藏加载对话框"""
        if hasattr(self, 'loading_dialog'):
            if hasattr(self, 'loading_spinner'):
                self.loading_spinner.stop_spinning()
            self.loading_dialog.destroy()

    def show_database_setup_dialog(self, error_msg=""):
        """显示数据库设置对话框"""
        dialog = Toplevel(self.root)
        dialog.title("数据库设置")
        dialog.geometry("500x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 100,
            self.root.winfo_rooty() + 100
        ))

        # 主框架
        main_frame = tk.Frame(dialog, bg="#ffffff", padx=30, pady=30)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = tk.Label(
            main_frame,
            text="数据库连接设置",
            font=("Arial", 16, "bold"),
            bg="#ffffff",
            fg="#1f2937"
        )
        title_label.pack(pady=(0, 20))

        # 错误信息
        if error_msg:
            error_frame = tk.Frame(main_frame, bg="#fef2f2", relief="solid", borderwidth=1)
            error_frame.pack(fill=tk.X, pady=(0, 20))

            tk.Label(
                error_frame,
                text="❌ 连接失败",
                font=("Arial", 10, "bold"),
                bg="#fef2f2",
                fg="#dc2626"
            ).pack(anchor="w", padx=10, pady=(10, 5))

            tk.Label(
                error_frame,
                text=error_msg,
                font=("Arial", 9),
                bg="#fef2f2",
                fg="#7f1d1d",
                wraplength=400
            ).pack(anchor="w", padx=10, pady=(0, 10))

        # 设置说明
        info_frame = tk.Frame(main_frame, bg="#f0f9ff", relief="solid", borderwidth=1)
        info_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(
            info_frame,
            text="💡 数据库设置说明",
            font=("Arial", 10, "bold"),
            bg="#f0f9ff",
            fg="#0369a1"
        ).pack(anchor="w", padx=10, pady=(10, 5))

        info_text = """请确保MySQL服务正在运行，并使用以下默认设置：
• 主机：localhost
• 端口：3306
• 用户名：root
• 密码：XZJ020601

如需修改设置，请编辑 database.py 文件中的连接参数。"""

        tk.Label(
            info_frame,
            text=info_text,
            font=("Arial", 9),
            bg="#f0f9ff",
            fg="#0c4a6e",
            justify=tk.LEFT
        ).pack(anchor="w", padx=10, pady=(0, 10))

        # 按钮
        btn_frame = tk.Frame(main_frame, bg="#ffffff")
        btn_frame.pack(fill=tk.X, pady=(20, 0))

        def retry_connection():
            dialog.destroy()
            if self.init_database():
                self.load_pokemon_data()

        def exit_app():
            dialog.destroy()
            self.root.quit()

        ModernButton(
            btn_frame,
            text="🔄 重试连接",
            command=retry_connection,
            style="primary"
        ).pack(side=tk.LEFT, padx=(0, 10))

        ModernButton(
            btn_frame,
            text="❌ 退出程序",
            command=exit_app,
            style="danger"
        ).pack(side=tk.LEFT)

    def calculate_breeding_plan(self):
        """计算最优繁殖方案"""
        try:
            # 获取目标个体值
            target_ivs = {}
            target_v_count = 0
            for attr, var in self.target_iv_vars.items():
                value = var.get()
                target_ivs[attr] = value
                if value == 31:
                    target_v_count += 1
            
            if target_v_count == 0:
                messagebox.showinfo("提示", "请至少设置一个目标31的个体值")
                return
            
            # 获取目标性格
            target_nature = self.target_nature_var.get()
            if target_nature == "任意":
                target_nature = None
            elif " (" in target_nature:
                # 提取纯性格名称
                target_nature = target_nature.split(" (")[0]
            
            # 获取目标性别和比例
            target_gender = self.target_gender_var.get()
            if target_gender == "不锁定":
                target_gender = None
                
            gender_ratio = self.ratio_var.get()
            
            # 获取素材宝可梦列表
            materials = []
            if self.materials_var.get():
                # 使用所有素材
                materials = self.pokemon_list
            else:
                # 获取选中的素材
                selection = self.materials_listbox.curselection()
                if selection:
                    for i in selection:
                        materials.append(self.pokemon_list[i])
                else:
                    messagebox.showinfo("提示", "请选择至少一个繁殖素材")
                    return
            
            if not materials:
                messagebox.showinfo("提示", "没有可用的繁殖素材")
                return
                
            # 清空结果显示
            self.result_text.delete(1.0, tk.END)
            self.plan_text.delete(1.0, tk.END)
            
            # 显示计算中信息
            self.result_text.insert(tk.END, "正在计算最优繁殖方案...\n")
            self.result_text.update()
            
            # 计算性别锁定成本
            gender_cost = 0
            if target_gender:
                if gender_ratio == "1:1":
                    gender_cost = 5000
                elif gender_ratio == "1:3":
                    gender_cost = 9000 if target_gender == "公" else 5000
                elif gender_ratio == "3:1":
                    gender_cost = 5000 if target_gender == "公" else 9000
                elif gender_ratio == "7:1" or gender_ratio == "1:7":
                    gender_cost = 21000
            
            # 计算最优繁殖方案
            best_plan = self.find_optimal_breeding_plan(target_ivs, target_nature, materials)
            
            if not best_plan:
                self.result_text.delete(1.0, tk.END)
                self.result_text.insert(tk.END, "无法找到有效的繁殖方案，请检查繁殖素材和目标。")
                return
            
            # 总成本
            total_cost = best_plan["total_cost"] + gender_cost
            
            # 显示结果
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, f"目标: {target_v_count}V 宝可梦\n\n")
            
            # 显示目标个体值
            self.result_text.insert(tk.END, "目标个体值:\n")
            for attr, value in target_ivs.items():
                if value == 31:
                    attr_name = next(label for a, label in [("hp", "HP"), ("attack", "攻击"), 
                                                         ("defense", "防御"), ("sp_attack", "特攻"), 
                                                         ("sp_defense", "特防"), ("speed", "速度")] if a == attr)
                    self.result_text.insert(tk.END, f"- {attr_name}: {value}\n")
            
            # 显示目标性格和性别
            if target_nature:
                self.result_text.insert(tk.END, f"\n目标性格: {target_nature}\n")
                
            if target_gender:
                self.result_text.insert(tk.END, f"\n目标性别: {target_gender} ({gender_ratio})\n")
                self.result_text.insert(tk.END, f"性别锁定成本: {gender_cost:,}\n")
                
            self.result_text.insert(tk.END, f"\n繁殖成本: {best_plan['total_cost']:,}\n")
            self.result_text.insert(tk.END, f"总成本: {total_cost:,}\n")
            
            # 显示详细方案
            self.show_breeding_plan_details(best_plan)
            
        except Exception as e:
            messagebox.showerror("计算错误", f"计算最优繁殖方案时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def find_optimal_breeding_plan(self, target_ivs, target_nature, materials):
        """寻找最优繁殖方案"""
        # 筛选出符合目标个体值的宝可梦
        candidates = []
        for pokemon in materials:
            # 检查个体值
            matches = True
            for attr, value in target_ivs.items():
                if value == 31 and pokemon.ivs.get(attr, 0) != 31:
                    matches = False
                    break
            
            # 检查性格
            if target_nature and pokemon.nature != target_nature:
                matches = False
            
            if matches:
                candidates.append(pokemon)
        
        # 如果有直接符合条件的宝可梦，选择价格最低的
        if candidates:
            best_pokemon = min(candidates, key=lambda p: p.price)
            return {
                "type": "direct",
                "pokemon": best_pokemon,
                "total_cost": best_pokemon.price,
                "steps": [
                    {
                        "type": "purchase",
                        "pokemon": best_pokemon,
                        "cost": best_pokemon.price
                    }
                ]
            }
        
        # 如果没有直接符合条件的，计算繁殖方案
        return self.calculate_breeding_steps(target_ivs, target_nature, materials)
    
    def calculate_breeding_steps(self, target_ivs, target_nature, materials):
        """计算繁殖步骤"""
        # 简化版本：按V数和属性匹配度对素材排序，选择最适合的组合
        target_attrs = [attr for attr, value in target_ivs.items() if value == 31]
        
        # 如果没有目标个体值，直接返回
        if not target_attrs:
            return None
        
        # 对素材按个体值匹配度排序
        sorted_materials = []
        for pokemon in materials:
            # 计算匹配的31个体值数量
            match_count = sum(1 for attr in target_attrs if pokemon.ivs.get(attr, 0) == 31)
            # 计算总V数
            total_v_count = pokemon.get_v_count()
            
            # 只考虑至少有一个目标31个体值的宝可梦
            if match_count > 0:
                sorted_materials.append((pokemon, match_count, total_v_count))
        
        # 按匹配度和总V数排序
        sorted_materials.sort(key=lambda x: (x[1], x[2]), reverse=True)
        
        # 如果没有符合条件的素材，返回None
        if not sorted_materials:
            return None
        
        # 选择最佳的父母组合
        best_pair = None
        best_score = -1
        best_cost = float('inf')
        
        # 尝试不同的组合
        for i, (mother, m_match, m_total) in enumerate(sorted_materials):
            for j, (father, f_match, f_total) in enumerate(sorted_materials):
                if i == j:  # 不能使用同一个宝可梦
                    continue
                    
                # 计算组合得分
                combined_match = 0
                for attr in target_attrs:
                    if mother.ivs.get(attr, 0) == 31 or father.ivs.get(attr, 0) == 31:
                        combined_match += 1
                
                # 计算组合成本
                combined_cost = mother.price + father.price
                
                # 如果匹配度更高或相同但成本更低，更新最佳组合
                if combined_match > best_score or (combined_match == best_score and combined_cost < best_cost):
                    best_pair = (mother, father)
                    best_score = combined_match
                    best_cost = combined_cost
        
        # 如果找不到合适的组合，返回None
        if not best_pair:
            return None
            
        mother, father = best_pair
        
        # 计算繁殖结果
        child_ivs = self.predict_child_ivs(mother, father)
        
        # 计算需要继续繁殖的代数
        breeding_steps = 1
        current_ivs = child_ivs
        
        while not all(current_ivs.get(attr, 0) == 31 for attr in target_attrs):
            # 模拟下一代繁殖
            # 这里简化处理，假设每代提高一个31个体值
            breeding_steps += 1
            
            # 超过一定代数，认为方案不可行
            if breeding_steps > 5:
                return None
        
        # 计算总成本
        # 假设每次繁殖成本是父母宝可梦价格的一半
        breeding_cost = best_cost + (breeding_steps - 1) * (best_cost / 2)
        
        # 返回繁殖方案
        return {
            "type": "breeding",
            "parents": best_pair,
            "steps": breeding_steps,
            "total_cost": breeding_cost,
            "steps": [
                {
                    "type": "purchase",
                    "pokemon": mother,
                    "cost": mother.price
                },
                {
                    "type": "purchase",
                    "pokemon": father,
                    "cost": father.price
                },
                {
                    "type": "breeding",
                    "steps": breeding_steps,
                    "cost": (breeding_steps - 1) * (best_cost / 2)
                }
            ]
        }
    
    def predict_child_ivs(self, mother, father):
        """预测子代个体值"""
        child_ivs = {}
        
        # 简化处理：子代继承父母个体值的最大值
        for attr in ["hp", "attack", "defense", "sp_attack", "sp_defense", "speed"]:
            mother_iv = mother.ivs.get(attr, 0)
            father_iv = father.ivs.get(attr, 0)
            child_ivs[attr] = max(mother_iv, father_iv)
            
        return child_ivs
    
    def show_breeding_plan_details(self, plan):
        """显示繁殖计划详情"""
        self.plan_text.delete(1.0, tk.END)
        
        if plan["type"] == "direct":
            self.plan_text.insert(tk.END, "直接购买方案:\n\n")
            pokemon = plan["pokemon"]
            self.plan_text.insert(tk.END, f"宝可梦: {pokemon.display_name}\n")
            self.plan_text.insert(tk.END, f"种类: {pokemon.species_name}\n")
            self.plan_text.insert(tk.END, f"个体值: {format_pokemon_ivs(pokemon.ivs)}\n")
            self.plan_text.insert(tk.END, f"性格: {get_nature_display_name(pokemon.nature)}\n")
            self.plan_text.insert(tk.END, f"价格: {pokemon.price:,}\n")
        else:
            self.plan_text.insert(tk.END, "繁殖方案:\n\n")

            mother, father = plan["parents"]
            self.plan_text.insert(tk.END, f"母体: {mother.display_name}\n")
            self.plan_text.insert(tk.END, f"种类: {mother.species_name}\n")
            self.plan_text.insert(tk.END, f"个体值: {format_pokemon_ivs(mother.ivs)}\n")
            self.plan_text.insert(tk.END, f"价格: {mother.price:,}\n\n")

            self.plan_text.insert(tk.END, f"父体: {father.display_name}\n")
            self.plan_text.insert(tk.END, f"种类: {father.species_name}\n")
            self.plan_text.insert(tk.END, f"个体值: {format_pokemon_ivs(father.ivs)}\n")
            self.plan_text.insert(tk.END, f"价格: {father.price:,}\n\n")

            self.plan_text.insert(tk.END, f"预计繁殖代数: {plan['steps']}\n")
            self.plan_text.insert(tk.END, f"总成本: {plan['total_cost']:,}\n")

    def on_closing(self):
        """应用关闭时的清理工作"""
        try:
            # 关闭数据库连接
            if self.db:
                self.db.disconnect()
        except Exception as e:
            print(f"关闭数据库连接时出错: {e}")
        finally:
            # 关闭应用
            self.root.destroy()

# 创建主应用
def main():
    root = tk.Tk()

    # 设置窗口图标（如果有的话）
    try:
        # root.iconbitmap("icon.ico")  # 如果有图标文件
        pass
    except:
        pass

    # 创建应用
    app = PokemmoBreedingCalculator(root)

    # 运行应用
    root.mainloop()

if __name__ == "__main__":
    main() 