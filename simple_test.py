#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的UI测试脚本
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from ui_components import ModernButton, ModernFrame, apply_modern_theme
    
    def test_ui():
        root = tk.Tk()
        root.title("UI测试")
        root.geometry("600x400")
        
        # 应用主题
        apply_modern_theme(root)
        
        # 创建主框架
        main_frame = ModernFrame(root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title = tk.Label(
            main_frame,
            text="🎮 UI美化测试",
            font=("Segoe UI", 18, "bold"),
            bg="#ffffff",
            fg="#0f172a"
        )
        title.pack(pady=(0, 20))
        
        # 按钮测试
        button_frame = tk.Frame(main_frame, bg="#ffffff")
        button_frame.pack(fill=tk.X, pady=10)
        
        # 不同样式的按钮
        btn1 = ModernButton(button_frame, text="主要按钮", style="primary")
        btn1.pack(side=tk.LEFT, padx=(0, 10))
        
        btn2 = ModernButton(button_frame, text="次要按钮", style="secondary")
        btn2.pack(side=tk.LEFT, padx=(0, 10))
        
        btn3 = ModernButton(button_frame, text="成功按钮", style="success")
        btn3.pack(side=tk.LEFT, padx=(0, 10))
        
        btn4 = ModernButton(button_frame, text="危险按钮", style="danger")
        btn4.pack(side=tk.LEFT)
        
        # 信息文本
        info_text = tk.Label(
            main_frame,
            text="✅ UI组件加载成功！\n现在的界面使用了现代化的设计风格，包括：\n• 更好的颜色搭配\n• 现代化的按钮样式\n• 改进的间距和布局\n• 更清晰的视觉层次",
            font=("Segoe UI", 11),
            bg="#ffffff",
            fg="#475569",
            justify=tk.LEFT
        )
        info_text.pack(pady=20, anchor="w")
        
        root.mainloop()
    
    if __name__ == "__main__":
        test_ui()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保ui_components.py文件存在且正确")
except Exception as e:
    print(f"运行错误: {e}")
    import traceback
    traceback.print_exc()
