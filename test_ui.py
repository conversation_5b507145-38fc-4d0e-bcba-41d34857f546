#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
UI美化测试脚本
用于测试新的UI组件和样式
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui_components import (
    ModernButton, ModernFrame, ModernEntry, ModernCombobox,
    PokemonCard, ModernNotebook, GradientFrame, ModernTheme,
    apply_modern_theme, TooltipLabel, ProgressCard
)

class UITestApp:
    """UI测试应用"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("POKEMMO GUI 美化测试")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # 应用现代主题
        self.style = apply_modern_theme(self.root)
        self.theme = ModernTheme()
        
        self.create_ui()
    
    def create_ui(self):
        """创建测试UI"""
        # 主容器
        main_frame = ModernFrame(self.root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题栏
        self.create_header(main_frame)
        
        # 标签页
        self.create_tabs(main_frame)
    
    def create_header(self, parent):
        """创建标题栏"""
        header = GradientFrame(
            parent,
            height=80,
            color1="#4f46e5",
            color2="#7c3aed",
            direction="horizontal"
        )
        header.pack(fill=tk.X, pady=(0, 20))
        
        # 标题容器
        title_container = tk.Frame(header, bg="transparent")
        title_container.place(relx=0.5, rely=0.5, anchor="center")
        
        title = tk.Label(
            title_container,
            text="🎮 POKEMMO GUI 美化测试",
            font=("Segoe UI", 18, "bold"),
            bg="transparent",
            fg="white"
        )
        title.pack()
        
        subtitle = tk.Label(
            title_container,
            text="现代化UI组件展示",
            font=("Segoe UI", 11),
            bg="transparent",
            fg="white"
        )
        subtitle.pack(pady=(2, 0))
    
    def create_tabs(self, parent):
        """创建标签页"""
        notebook = ModernNotebook(parent)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 按钮测试页
        self.create_button_test_tab(notebook)
        
        # 表单测试页
        self.create_form_test_tab(notebook)
        
        # 卡片测试页
        self.create_card_test_tab(notebook)
    
    def create_button_test_tab(self, notebook):
        """创建按钮测试页"""
        tab = tk.Frame(notebook, bg=self.theme.COLORS["bg_secondary"])
        notebook.add(tab, text="🔘 按钮测试")
        
        container = ModernFrame(tab, padding=24)
        container.pack(fill=tk.BOTH, expand=True, padx=24, pady=24)
        
        # 标题
        title = tk.Label(
            container,
            text="按钮样式展示",
            font=self.theme.FONTS["title"],
            bg=self.theme.COLORS["white"],
            fg=self.theme.COLORS["text_primary"]
        )
        title.pack(anchor="w", pady=(0, 20))
        
        # 按钮网格
        button_grid = tk.Frame(container, bg=self.theme.COLORS["white"])
        button_grid.pack(fill=tk.X, pady=(0, 20))
        
        # 不同样式的按钮
        styles = [
            ("primary", "主要按钮"),
            ("secondary", "次要按钮"),
            ("outline", "轮廓按钮"),
            ("ghost", "幽灵按钮"),
            ("success", "成功按钮"),
            ("warning", "警告按钮"),
            ("danger", "危险按钮"),
            ("info", "信息按钮")
        ]
        
        for i, (style, text) in enumerate(styles):
            row = i // 4
            col = i % 4
            
            btn = ModernButton(
                button_grid,
                text=text,
                style=style,
                command=lambda s=style: self.show_message(f"点击了{s}按钮")
            )
            btn.grid(row=row, column=col, padx=8, pady=8, sticky="ew")
        
        # 配置网格权重
        for i in range(4):
            button_grid.columnconfigure(i, weight=1)
    
    def create_form_test_tab(self, notebook):
        """创建表单测试页"""
        tab = tk.Frame(notebook, bg=self.theme.COLORS["bg_secondary"])
        notebook.add(tab, text="📝 表单测试")
        
        container = ModernFrame(tab, padding=24)
        container.pack(fill=tk.BOTH, expand=True, padx=24, pady=24)
        
        # 标题
        title = tk.Label(
            container,
            text="表单组件展示",
            font=self.theme.FONTS["title"],
            bg=self.theme.COLORS["white"],
            fg=self.theme.COLORS["text_primary"]
        )
        title.pack(anchor="w", pady=(0, 20))
        
        # 表单区域
        form_area = tk.Frame(container, bg=self.theme.COLORS["white"])
        form_area.pack(fill=tk.X, pady=(0, 20))
        
        # 输入框
        entry_frame = tk.Frame(form_area, bg=self.theme.COLORS["white"])
        entry_frame.pack(fill=tk.X, pady=(0, 16))
        
        tk.Label(
            entry_frame,
            text="现代化输入框:",
            font=self.theme.FONTS["default"],
            bg=self.theme.COLORS["white"],
            fg=self.theme.COLORS["text_primary"]
        ).pack(anchor="w", pady=(0, 4))
        
        entry = ModernEntry(entry_frame, placeholder="请输入内容...")
        entry.pack(fill=tk.X)
        
        # 下拉框
        combo_frame = tk.Frame(form_area, bg=self.theme.COLORS["white"])
        combo_frame.pack(fill=tk.X, pady=(0, 16))
        
        tk.Label(
            combo_frame,
            text="现代化下拉框:",
            font=self.theme.FONTS["default"],
            bg=self.theme.COLORS["white"],
            fg=self.theme.COLORS["text_primary"]
        ).pack(anchor="w", pady=(0, 4))
        
        combo = ModernCombobox(
            combo_frame,
            values=["选项1", "选项2", "选项3"],
            state="readonly"
        )
        combo.pack(fill=tk.X)
        
        # 进度卡片
        progress = ProgressCard(
            form_area,
            title="繁殖进度",
            current=7,
            total=10
        )
        progress.pack(fill=tk.X, pady=(16, 0))
    
    def create_card_test_tab(self, notebook):
        """创建卡片测试页"""
        tab = tk.Frame(notebook, bg=self.theme.COLORS["bg_secondary"])
        notebook.add(tab, text="🎴 卡片测试")
        
        container = ModernFrame(tab, padding=24)
        container.pack(fill=tk.BOTH, expand=True, padx=24, pady=24)
        
        # 标题
        title = tk.Label(
            container,
            text="卡片组件展示",
            font=self.theme.FONTS["title"],
            bg=self.theme.COLORS["white"],
            fg=self.theme.COLORS["text_primary"]
        )
        title.pack(anchor="w", pady=(0, 20))
        
        # 卡片网格
        card_grid = tk.Frame(container, bg=self.theme.COLORS["white"])
        card_grid.pack(fill=tk.BOTH, expand=True)
        
        # 创建一些测试卡片
        for i in range(12):
            row = i // 6
            col = i % 6
            
            # 模拟宝可梦数据
            class MockPokemon:
                def __init__(self, name, v_count):
                    self.species_name = name
                    self.nickname = ""
                    self.is_locked = i % 3 == 0
                    self.is_shiny = i % 5 == 0
                    self.v_count_value = v_count
                
                def get_v_count(self):
                    return self.v_count_value
            
            pokemon = MockPokemon(f"宝可梦{i+1}", i % 7)
            
            card = PokemonCard(
                card_grid,
                pokemon,
                on_click=lambda p: self.show_message(f"点击了{p.species_name}")
            )
            card.grid(row=row, column=col, padx=4, pady=4)
    
    def show_message(self, message):
        """显示消息"""
        print(f"测试消息: {message}")
    
    def run(self):
        """运行应用"""
        self.root.mainloop()

if __name__ == "__main__":
    app = UITestApp()
    app.run()
