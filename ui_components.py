#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
美化的UI组件库
类似Tailwind CSS的组件风格
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional, Callable
import math

# 现代化主题配置
class ModernTheme:
    """现代化主题配置类 - 采用更现代的配色方案"""

    # 主色调配置 - 使用更温和的蓝色系
    COLORS = {
        # 主要颜色 - 更现代的蓝色
        "primary": "#4f46e5",
        "primary_hover": "#4338ca",
        "primary_active": "#3730a3",
        "primary_light": "#e0e7ff",
        "primary_lighter": "#f0f4ff",

        # 次要颜色 - 更柔和的灰色
        "secondary": "#64748b",
        "secondary_hover": "#475569",
        "secondary_active": "#334155",
        "secondary_light": "#f1f5f9",

        # 状态颜色 - 更现代的配色
        "success": "#059669",
        "success_hover": "#047857",
        "success_light": "#d1fae5",
        "success_lighter": "#ecfdf5",

        "warning": "#d97706",
        "warning_hover": "#b45309",
        "warning_light": "#fef3c7",
        "warning_lighter": "#fffbeb",

        "danger": "#dc2626",
        "danger_hover": "#b91c1c",
        "danger_light": "#fee2e2",
        "danger_lighter": "#fef2f2",

        "info": "#0891b2",
        "info_hover": "#0e7490",
        "info_light": "#cffafe",
        "info_lighter": "#f0fdfa",

        # 中性颜色 - 更丰富的灰色层次
        "white": "#ffffff",
        "gray_50": "#f8fafc",
        "gray_100": "#f1f5f9",
        "gray_200": "#e2e8f0",
        "gray_300": "#cbd5e1",
        "gray_400": "#94a3b8",
        "gray_500": "#64748b",
        "gray_600": "#475569",
        "gray_700": "#334155",
        "gray_800": "#1e293b",
        "gray_900": "#0f172a",

        # 背景颜色 - 更层次化的背景
        "bg_primary": "#ffffff",
        "bg_secondary": "#f8fafc",
        "bg_tertiary": "#f1f5f9",
        "bg_quaternary": "#e2e8f0",

        # 文字颜色 - 更好的对比度
        "text_primary": "#0f172a",
        "text_secondary": "#475569",
        "text_tertiary": "#64748b",
        "text_quaternary": "#94a3b8",
        "text_inverse": "#ffffff",

        # 边框颜色 - 更细腻的边框
        "border_light": "#e2e8f0",
        "border_medium": "#cbd5e1",
        "border_dark": "#94a3b8",
        "border_focus": "#4f46e5",

        # 阴影颜色
        "shadow_light": "rgba(15, 23, 42, 0.04)",
        "shadow_medium": "rgba(15, 23, 42, 0.08)",
        "shadow_dark": "rgba(15, 23, 42, 0.12)",
    }

    # 字体配置 - 更现代的字体层次
    FONTS = {
        "default": ("Segoe UI", 10),
        "small": ("Segoe UI", 9),
        "large": ("Segoe UI", 11),
        "heading": ("Segoe UI", 13, "bold"),
        "title": ("Segoe UI", 18, "bold"),
        "large_title": ("Segoe UI", 24, "bold"),
        "subtitle": ("Segoe UI", 12),
        "caption": ("Segoe UI", 8),
        "code": ("JetBrains Mono", 9),
        "button": ("Segoe UI", 10, "normal"),
        "button_small": ("Segoe UI", 9, "normal"),
    }

    # 尺寸配置 - 更精细的尺寸控制
    SIZES = {
        "border_radius": 8,
        "border_radius_small": 6,
        "border_radius_large": 12,
        "button_height": 40,
        "button_height_small": 32,
        "button_height_large": 48,
        "input_height": 40,
        "input_height_small": 32,
        "card_padding": 20,
        "card_padding_small": 16,
        "card_padding_large": 24,
        "section_spacing": 24,
        "element_spacing": 12,
        "small_spacing": 8,
        "tiny_spacing": 4,
        "large_spacing": 16,
    }

    # 阴影配置 - 更现代的阴影效果
    SHADOWS = {
        "none": {"offset": (0, 0), "blur": 0, "color": "transparent"},
        "xs": {"offset": (0, 1), "blur": 2, "color": "rgba(15, 23, 42, 0.05)"},
        "sm": {"offset": (0, 1), "blur": 3, "color": "rgba(15, 23, 42, 0.1)"},
        "md": {"offset": (0, 4), "blur": 6, "color": "rgba(15, 23, 42, 0.1)"},
        "lg": {"offset": (0, 10), "blur": 15, "color": "rgba(15, 23, 42, 0.1)"},
        "xl": {"offset": (0, 20), "blur": 25, "color": "rgba(15, 23, 42, 0.1)"},
        "inner": {"offset": (0, 2), "blur": 4, "color": "rgba(15, 23, 42, 0.06)", "inset": True},
    }

class ModernButton(tk.Button):
    """现代化按钮组件 - 支持更好的视觉效果和交互"""

    def __init__(self, parent, text="", command=None, style="primary", size="medium", icon="", **kwargs):
        self.parent = parent
        self.text = text
        self.command = command
        self.style_type = style
        self.size = size
        self.icon = icon
        self.is_hovered = False
        self.is_pressed = False
        self.is_disabled = False

        # 获取样式配置
        self.theme = ModernTheme()
        self.setup_style_config()

        # 处理图标和文本
        display_text = f"{icon} {text}" if icon else text

        # 创建按钮
        super().__init__(
            parent,
            text=display_text,
            command=command,
            font=self.font,
            fg=self.fg_color,
            bg=self.bg_color,
            activebackground=self.hover_bg_color,
            activeforeground=self.fg_color,
            relief="flat",
            borderwidth=0,
            cursor="hand2",
            **kwargs
        )

        # 设置按钮尺寸和样式
        self.configure(
            height=self.button_height,
            padx=self.padding_x,
            pady=self.padding_y
        )

        # 绑定事件
        self.bind("<Enter>", self.on_enter)
        self.bind("<Leave>", self.on_leave)
        self.bind("<Button-1>", self.on_press)
        self.bind("<ButtonRelease-1>", self.on_release)

    def setup_style_config(self):
        """设置样式配置"""
        # 颜色配置 - 更丰富的样式选项
        color_map = {
            "primary": (self.theme.COLORS["primary"], self.theme.COLORS["primary_hover"], self.theme.COLORS["text_inverse"]),
            "secondary": (self.theme.COLORS["gray_100"], self.theme.COLORS["gray_200"], self.theme.COLORS["text_primary"]),
            "outline": (self.theme.COLORS["white"], self.theme.COLORS["gray_50"], self.theme.COLORS["primary"]),
            "ghost": (self.theme.COLORS["white"], self.theme.COLORS["primary_lighter"], self.theme.COLORS["primary"]),
            "success": (self.theme.COLORS["success"], self.theme.COLORS["success_hover"], self.theme.COLORS["text_inverse"]),
            "warning": (self.theme.COLORS["warning"], self.theme.COLORS["warning_hover"], self.theme.COLORS["text_inverse"]),
            "danger": (self.theme.COLORS["danger"], self.theme.COLORS["danger_hover"], self.theme.COLORS["text_inverse"]),
            "info": (self.theme.COLORS["info"], self.theme.COLORS["info_hover"], self.theme.COLORS["text_inverse"]),
        }

        self.bg_color, self.hover_bg_color, self.fg_color = color_map.get(self.style_type, color_map["primary"])

        # 尺寸配置 - 更精细的尺寸控制
        size_map = {
            "small": (self.theme.SIZES["button_height_small"], 12, 4, self.theme.FONTS["button_small"]),
            "medium": (self.theme.SIZES["button_height"], 16, 6, self.theme.FONTS["button"]),
            "large": (self.theme.SIZES["button_height_large"], 20, 8, self.theme.FONTS["heading"]),
        }

        self.button_height, self.padding_x, self.padding_y, self.font = size_map.get(self.size, size_map["medium"])

        # 特殊样式处理
        if self.style_type == "outline":
            self.configure(relief="solid", borderwidth=1, highlightbackground=self.theme.COLORS["primary"])
        elif self.style_type == "ghost":
            self.configure(relief="flat", borderwidth=0)

    def on_enter(self, event):
        """鼠标进入事件"""
        self.is_hovered = True
        self.configure(bg=self.hover_bg_color)

    def on_leave(self, event):
        """鼠标离开事件"""
        self.is_hovered = False
        if not self.is_pressed:
            self.configure(bg=self.bg_color)

    def on_press(self, event):
        """鼠标按下事件"""
        self.is_pressed = True
        # 添加按下效果（稍微变暗）
        pressed_color = self.darken_color(self.hover_bg_color, 0.1)
        self.configure(bg=pressed_color)

    def on_release(self, event):
        """鼠标释放事件"""
        self.is_pressed = False
        if self.is_hovered:
            self.configure(bg=self.hover_bg_color)
        else:
            self.configure(bg=self.bg_color)

    def darken_color(self, color, factor):
        """使颜色变暗"""
        # 简单的颜色变暗算法
        if color.startswith('#'):
            color = color[1:]

        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)

        r = max(0, int(r * (1 - factor)))
        g = max(0, int(g * (1 - factor)))
        b = max(0, int(b * (1 - factor)))

        return f"#{r:02x}{g:02x}{b:02x}"

class LoadingSpinner(tk.Canvas):
    """加载动画组件"""

    def __init__(self, parent, size=32, color=None, **kwargs):
        self.theme = ModernTheme()
        self.size = size
        self.color = color or self.theme.COLORS["primary"]
        self.angle = 0
        self.is_spinning = False

        super().__init__(
            parent,
            width=size,
            height=size,
            bg=self.theme.COLORS["bg_primary"],
            highlightthickness=0,
            **kwargs
        )

        self.center_x = size // 2
        self.center_y = size // 2
        self.radius = size // 3

    def start_spinning(self):
        """开始旋转动画"""
        self.is_spinning = True
        self.spin()

    def stop_spinning(self):
        """停止旋转动画"""
        self.is_spinning = False

    def spin(self):
        """旋转动画"""
        if not self.is_spinning:
            return

        self.delete("all")

        # 绘制旋转的圆弧
        for i in range(8):
            start_angle = self.angle + i * 45
            alpha = 1.0 - (i * 0.1)

            # 计算颜色透明度（简化版）
            color = self.color
            if alpha < 1.0:
                # 简单的透明度模拟
                color = self.theme.COLORS["gray_300"]

            # 绘制小圆点
            x = self.center_x + self.radius * math.cos(math.radians(start_angle))
            y = self.center_y + self.radius * math.sin(math.radians(start_angle))

            self.create_oval(
                x - 2, y - 2, x + 2, y + 2,
                fill=color,
                outline=""
            )

        self.angle = (self.angle + 45) % 360
        self.after(100, self.spin)

class GradientFrame(tk.Canvas):
    """渐变背景框架"""

    def __init__(self, parent, width=200, height=100, color1="#3b82f6", color2="#1d4ed8", direction="vertical", **kwargs):
        super().__init__(parent, width=width, height=height, highlightthickness=0, **kwargs)

        self.width = width
        self.height = height
        self.color1 = color1
        self.color2 = color2
        self.direction = direction

        self.bind("<Configure>", self.on_resize)
        self.draw_gradient()

    def on_resize(self, event):
        """窗口大小改变时重绘渐变"""
        self.width = event.width
        self.height = event.height
        self.draw_gradient()

    def draw_gradient(self):
        """绘制渐变背景"""
        self.delete("all")

        # 解析颜色
        r1, g1, b1 = self.hex_to_rgb(self.color1)
        r2, g2, b2 = self.hex_to_rgb(self.color2)

        if self.direction == "vertical":
            steps = self.height
            for i in range(steps):
                ratio = i / steps
                r = int(r1 + (r2 - r1) * ratio)
                g = int(g1 + (g2 - g1) * ratio)
                b = int(b1 + (b2 - b1) * ratio)

                color = f"#{r:02x}{g:02x}{b:02x}"
                self.create_line(0, i, self.width, i, fill=color, width=1)
        else:  # horizontal
            steps = self.width
            for i in range(steps):
                ratio = i / steps
                r = int(r1 + (r2 - r1) * ratio)
                g = int(g1 + (g2 - g1) * ratio)
                b = int(b1 + (b2 - b1) * ratio)

                color = f"#{r:02x}{g:02x}{b:02x}"
                self.create_line(i, 0, i, self.height, fill=color, width=1)

    def hex_to_rgb(self, hex_color):
        """将十六进制颜色转换为RGB"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

class ModernFrame(tk.Frame):
    """现代化框架组件 - 支持圆角和阴影"""

    def __init__(self, parent, padding=16, shadow=True, rounded=True, **kwargs):
        self.theme = ModernTheme()

        # 设置默认样式
        default_kwargs = {
            "bg": self.theme.COLORS["bg_primary"],
            "relief": "flat",
            "borderwidth": 0,
        }
        default_kwargs.update(kwargs)

        super().__init__(parent, **default_kwargs)

        # 添加内边距
        if padding:
            self.configure(padx=padding, pady=padding)

        # 如果需要阴影效果，可以通过边框模拟
        if shadow:
            self.configure(
                relief="solid",
                borderwidth=1,
                highlightbackground=self.theme.COLORS["border_light"],
                highlightthickness=1
            )

class ModernLabelFrame(ttk.LabelFrame):
    """现代化标签框架"""
    
    def __init__(self, parent, text="", **kwargs):
        super().__init__(parent, text=text, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Modern.TLabelframe",
            background="#ffffff",
            borderwidth=1,
            relief="solid"
        )
        style.configure(
            "Modern.TLabelframe.Label",
            background="#ffffff",
            foreground="#1f2937",
            font=("Arial", 10, "bold")
        )
        self.configure(style="Modern.TLabelframe")

class ToggleButton(tk.Button):
    """切换按钮组件（用于个体值设置）"""
    
    def __init__(self, parent, variable, on_text="31", off_text="0", **kwargs):
        self.variable = variable
        self.on_text = on_text
        self.off_text = off_text
        
        # 颜色配置
        self.colors = {
            "on": {"bg": "#10b981", "fg": "white", "active_bg": "#059669"},
            "off": {"bg": "#6b7280", "fg": "white", "active_bg": "#4b5563"}
        }
        
        super().__init__(parent, command=self.toggle, **kwargs)
        self.update_appearance()
        
        # 监听变量变化
        self.variable.trace("w", lambda *args: self.update_appearance())
    
    def toggle(self):
        """切换状态"""
        current = self.variable.get()
        if current == 0:
            self.variable.set(31)
        else:
            self.variable.set(0)
    
    def update_appearance(self):
        """更新外观"""
        current = self.variable.get()
        if current == 31:
            color_scheme = self.colors["on"]
            text = self.on_text
        else:
            color_scheme = self.colors["off"]
            text = self.off_text
        
        self.configure(
            text=text,
            bg=color_scheme["bg"],
            fg=color_scheme["fg"],
            activebackground=color_scheme["active_bg"],
            activeforeground=color_scheme["fg"],
            relief="flat",
            borderwidth=0,
            font=("Arial", 9, "bold"),
            width=4,
            height=1
        )

class ModernTreeview(ttk.Treeview):
    """现代化树形视图"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Modern.Treeview",
            background="#ffffff",
            foreground="#1f2937",
            fieldbackground="#ffffff",
            borderwidth=1,
            relief="solid"
        )
        style.configure(
            "Modern.Treeview.Heading",
            background="#f3f4f6",
            foreground="#1f2937",
            font=("Arial", 9, "bold")
        )
        style.map(
            "Modern.Treeview",
            background=[("selected", "#3b82f6")],
            foreground=[("selected", "white")]
        )
        
        self.configure(style="Modern.Treeview")

class ModernEntry(tk.Entry):
    """现代化输入框 - 支持焦点效果和占位符"""

    def __init__(self, parent, placeholder="", **kwargs):
        self.theme = ModernTheme()
        self.placeholder = placeholder
        self.placeholder_active = False

        # 设置默认样式
        default_kwargs = {
            "font": self.theme.FONTS["default"],
            "bg": self.theme.COLORS["bg_primary"],
            "fg": self.theme.COLORS["text_primary"],
            "relief": "solid",
            "borderwidth": 1,
            "highlightthickness": 1,
            "highlightcolor": self.theme.COLORS["primary"],
            "highlightbackground": self.theme.COLORS["border_light"],
            "insertbackground": self.theme.COLORS["text_primary"],
        }
        default_kwargs.update(kwargs)

        super().__init__(parent, **default_kwargs)

        # 设置占位符
        if self.placeholder:
            self.set_placeholder()

        # 绑定焦点事件
        self.bind("<FocusIn>", self.on_focus_in)
        self.bind("<FocusOut>", self.on_focus_out)
        self.bind("<KeyPress>", self.on_key_press)

    def set_placeholder(self):
        """设置占位符"""
        self.placeholder_active = True
        self.configure(fg=self.theme.COLORS["text_tertiary"])
        self.insert(0, self.placeholder)

    def clear_placeholder(self):
        """清除占位符"""
        if self.placeholder_active:
            self.placeholder_active = False
            self.configure(fg=self.theme.COLORS["text_primary"])
            self.delete(0, tk.END)

    def on_focus_in(self, event):
        """获得焦点时"""
        self.configure(
            highlightcolor=self.theme.COLORS["primary"],
            highlightbackground=self.theme.COLORS["primary"]
        )
        self.clear_placeholder()

    def on_focus_out(self, event):
        """失去焦点时"""
        self.configure(
            highlightcolor=self.theme.COLORS["border_light"],
            highlightbackground=self.theme.COLORS["border_light"]
        )
        if not self.get() and self.placeholder:
            self.set_placeholder()

    def on_key_press(self, event):
        """按键时"""
        if self.placeholder_active:
            self.clear_placeholder()

class ModernCombobox(ttk.Combobox):
    """现代化下拉框"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Modern.TCombobox",
            fieldbackground="#ffffff",
            borderwidth=1,
            relief="solid",
            padding=8
        )
        
        self.configure(style="Modern.TCombobox")

class ModernSpinbox(ttk.Spinbox):
    """现代化数字输入框"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Modern.TSpinbox",
            fieldbackground="#ffffff",
            borderwidth=1,
            relief="solid",
            padding=8
        )
        
        self.configure(style="Modern.TSpinbox")

class CardFrame(tk.Frame):
    """卡片式框架"""
    
    def __init__(self, parent, title="", **kwargs):
        super().__init__(parent, **kwargs)
        
        # 设置卡片样式
        self.configure(
            bg="#ffffff",
            relief="solid",
            borderwidth=1,
            padx=16,
            pady=16
        )
        
        if title:
            title_label = tk.Label(
                self,
                text=title,
                bg="#ffffff",
                fg="#1f2937",
                font=("Arial", 12, "bold")
            )
            title_label.pack(anchor="w", pady=(0, 10))

class StatusBar(tk.Frame):
    """状态栏组件"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.configure(
            bg="#f3f4f6",
            relief="flat",
            borderwidth=1,
            height=30
        )
        
        self.status_label = tk.Label(
            self,
            text="就绪",
            bg="#f3f4f6",
            fg="#6b7280",
            font=("Arial", 9)
        )
        self.status_label.pack(side="left", padx=10, pady=5)
    
    def set_status(self, text: str, color: str = "#6b7280"):
        """设置状态文本"""
        self.status_label.configure(text=text, fg=color)

class PokemonCard(tk.Frame):
    """宝可梦卡片组件 - 更现代的卡片设计"""

    def __init__(self, parent, pokemon, on_click=None, **kwargs):
        super().__init__(parent, **kwargs)

        self.pokemon = pokemon
        self.on_click = on_click
        self.selected = False
        self.theme = ModernTheme()

        # 设置卡片样式 - 更现代的设计
        self.configure(
            bg=self.theme.COLORS["white"],
            relief="flat",
            borderwidth=0,
            width=100,
            height=120,
            cursor="hand2"
        )

        # 防止框架收缩
        self.pack_propagate(False)
        self.grid_propagate(False)

        # 创建阴影效果（通过边框模拟）
        try:
            self.shadow_frame = tk.Frame(
                parent,
                bg=self.theme.COLORS["gray_200"],
                width=102,
                height=122
            )
            self.shadow_frame.place(x=2, y=2)
            self.shadow_frame.lower()
        except:
            self.shadow_frame = None

        # 创建内容
        self.create_content()

        # 绑定点击事件
        self.bind("<Button-1>", self.on_card_click)
        self.bind("<Enter>", self.on_enter)
        self.bind("<Leave>", self.on_leave)

        # 为所有子组件绑定事件
        self.bind_all_children()

    def create_content(self):
        """创建卡片内容 - 更现代的布局"""
        # 主容器
        main_container = tk.Frame(self, bg=self.theme.COLORS["white"])
        main_container.pack(fill=tk.BOTH, expand=True, padx=8, pady=8)

        # 图片区域
        image_container = tk.Frame(main_container, bg=self.theme.COLORS["white"])
        image_container.pack(fill=tk.BOTH, expand=True)

        self.image_label = tk.Label(
            image_container,
            bg=self.theme.COLORS["white"],
            text="🎮",
            font=("Arial", 36),
            fg=self.theme.COLORS["gray_400"]
        )
        self.image_label.pack(expand=True)

        # 信息区域
        info_container = tk.Frame(main_container, bg=self.theme.COLORS["white"], height=24)
        info_container.pack(fill=tk.X, side=tk.BOTTOM)
        info_container.pack_propagate(False)

        # 宝可梦名称（简短显示）
        name = self.pokemon.nickname if self.pokemon.nickname else self.pokemon.species_name
        if len(name) > 8:
            name = name[:6] + "..."

        name_label = tk.Label(
            info_container,
            text=name,
            font=self.theme.FONTS["small"],
            bg=self.theme.COLORS["white"],
            fg=self.theme.COLORS["text_primary"]
        )
        name_label.pack(side=tk.LEFT)

        # V数标签
        v_count = self.pokemon.get_v_count()
        if v_count > 0:
            v_color = self.theme.COLORS["success"] if v_count >= 5 else self.theme.COLORS["gray_500"]
            v_bg = self.theme.COLORS["success_lighter"] if v_count >= 5 else self.theme.COLORS["gray_100"]

            v_label = tk.Label(
                info_container,
                text=f"{v_count}V",
                font=(self.theme.FONTS["small"][0], 8, "bold"),
                bg=v_bg,
                fg=v_color,
                padx=4,
                pady=1
            )
            v_label.pack(side=tk.RIGHT)

        # 状态图标叠加在右上角
        if self.has_status_icons():
            self.create_status_icons()

    def create_status_icons(self):
        """创建状态图标"""
        status_frame = tk.Frame(self, bg=self.theme.COLORS["white"])
        status_frame.place(relx=1.0, rely=0.0, anchor="ne", x=-4, y=4)

        status_icons = []
        if hasattr(self.pokemon, 'is_locked') and self.pokemon.is_locked:
            status_icons.append("🔒")
        if hasattr(self.pokemon, 'is_shiny') and self.pokemon.is_shiny:
            status_icons.append("✨")

        if status_icons:
            icon_label = tk.Label(
                status_frame,
                text="".join(status_icons),
                font=("Arial", 10),
                bg=self.theme.COLORS["white"],
                fg=self.theme.COLORS["text_primary"]
            )
            icon_label.pack()

    def has_status_icons(self):
        """检查是否有状态图标"""
        return ((hasattr(self.pokemon, 'is_locked') and self.pokemon.is_locked) or
                (hasattr(self.pokemon, 'is_shiny') and self.pokemon.is_shiny))

    def bind_all_children(self):
        """为所有子组件绑定事件"""
        def bind_recursive(widget):
            widget.bind("<Button-1>", self.on_card_click)
            for child in widget.winfo_children():
                bind_recursive(child)
        bind_recursive(self)

    def load_image(self, image_path):
        """加载宝可梦图片"""
        try:
            from PIL import Image, ImageTk
            img = Image.open(image_path)
            img = img.resize((64, 64), Image.LANCZOS)
            photo = ImageTk.PhotoImage(img)

            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo  # 保持引用
        except Exception as e:
            print(f"加载图片失败: {e}")

    def on_card_click(self, event):
        """卡片点击事件"""
        if self.on_click:
            self.on_click(self.pokemon)

    def on_enter(self, event):
        """鼠标进入"""
        if not self.selected:
            self.configure(bg=self.theme.COLORS["gray_50"])
            if hasattr(self, 'shadow_frame'):
                self.shadow_frame.configure(bg=self.theme.COLORS["gray_300"])

    def on_leave(self, event):
        """鼠标离开"""
        if not self.selected:
            self.configure(bg=self.theme.COLORS["white"])
            if hasattr(self, 'shadow_frame'):
                self.shadow_frame.configure(bg=self.theme.COLORS["gray_200"])

    def set_selected(self, selected):
        """设置选中状态"""
        self.selected = selected
        if selected:
            self.configure(bg=self.theme.COLORS["primary_lighter"])
            if hasattr(self, 'shadow_frame'):
                self.shadow_frame.configure(bg=self.theme.COLORS["primary"])
        else:
            self.configure(bg=self.theme.COLORS["white"])
            if hasattr(self, 'shadow_frame'):
                self.shadow_frame.configure(bg=self.theme.COLORS["gray_200"])

class FilterPanel(tk.Frame):
    """筛选面板组件"""

    def __init__(self, parent, on_filter_change=None, **kwargs):
        super().__init__(parent, **kwargs)

        self.on_filter_change = on_filter_change
        self.configure(bg="#ffffff", relief="solid", borderwidth=1)

        # 创建筛选控件
        self.create_filters()

    def create_filters(self):
        """创建筛选控件"""
        # 标题
        title_label = tk.Label(
            self,
            text="筛选条件",
            bg="#ffffff",
            fg="#1f2937",
            font=("Arial", 12, "bold")
        )
        title_label.pack(anchor="w", padx=10, pady=(10, 5))

        # 搜索框
        search_frame = tk.Frame(self, bg="#ffffff")
        search_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(
            search_frame,
            text="搜索:",
            bg="#ffffff",
            fg="#374151",
            font=("Arial", 10)
        ).pack(side=tk.LEFT)

        self.search_var = tk.StringVar()
        self.search_entry = ModernEntry(search_frame, textvariable=self.search_var)
        self.search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        self.search_var.trace("w", self.on_filter_changed)

        # 蛋组筛选
        egg_group_frame = tk.Frame(self, bg="#ffffff")
        egg_group_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(
            egg_group_frame,
            text="蛋组:",
            bg="#ffffff",
            fg="#374151",
            font=("Arial", 10)
        ).pack(side=tk.LEFT)

        self.egg_group_var = tk.StringVar(value="全部")
        self.egg_group_combo = ModernCombobox(
            egg_group_frame,
            textvariable=self.egg_group_var,
            values=["全部"],
            state="readonly",
            width=15
        )
        self.egg_group_combo.pack(side=tk.LEFT, padx=(5, 0))
        self.egg_group_combo.bind("<<ComboboxSelected>>", self.on_filter_changed)

        # V数筛选
        v_frame = tk.Frame(self, bg="#ffffff")
        v_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(
            v_frame,
            text="V数:",
            bg="#ffffff",
            fg="#374151",
            font=("Arial", 10)
        ).pack(side=tk.LEFT)

        self.v_count_var = tk.StringVar(value="全部")
        self.v_count_combo = ModernCombobox(
            v_frame,
            textvariable=self.v_count_var,
            values=["全部", "0V", "1V", "2V", "3V", "4V", "5V", "6V"],
            state="readonly",
            width=8
        )
        self.v_count_combo.pack(side=tk.LEFT, padx=(5, 0))
        self.v_count_combo.bind("<<ComboboxSelected>>", self.on_filter_changed)

        # 状态筛选
        status_frame = tk.Frame(self, bg="#ffffff")
        status_frame.pack(fill=tk.X, padx=10, pady=5)

        self.show_locked_var = tk.BooleanVar(value=True)
        self.show_locked_check = ttk.Checkbutton(
            status_frame,
            text="显示锁定",
            variable=self.show_locked_var,
            command=self.on_filter_changed
        )
        self.show_locked_check.pack(side=tk.LEFT)

        self.show_shiny_var = tk.BooleanVar(value=True)
        self.show_shiny_check = ttk.Checkbutton(
            status_frame,
            text="显示闪光",
            variable=self.show_shiny_var,
            command=self.on_filter_changed
        )
        self.show_shiny_check.pack(side=tk.LEFT, padx=(10, 0))

        # 重置按钮
        reset_btn = ModernButton(
            self,
            text="重置筛选",
            command=self.reset_filters,
            style="secondary"
        )
        reset_btn.pack(pady=10)

    def on_filter_changed(self, *args):
        """筛选条件改变"""
        if self.on_filter_change:
            filters = self.get_filters()
            self.on_filter_change(filters)

    def get_filters(self):
        """获取当前筛选条件"""
        return {
            'search': self.search_var.get().strip(),
            'egg_group': self.egg_group_var.get(),
            'v_count': self.v_count_var.get(),
            'show_locked': self.show_locked_var.get(),
            'show_shiny': self.show_shiny_var.get()
        }

    def reset_filters(self):
        """重置筛选条件"""
        self.search_var.set("")
        self.egg_group_var.set("全部")
        self.v_count_var.set("全部")
        self.show_locked_var.set(True)
        self.show_shiny_var.set(True)

    def update_egg_groups(self, egg_groups):
        """更新蛋组选项"""
        values = ["全部"] + egg_groups
        self.egg_group_combo.configure(values=values)

class ModernNotebook(ttk.Notebook):
    """现代化标签页组件"""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)

        # 设置现代化样式
        style = ttk.Style()

        # 配置标签页样式
        style.configure(
            "Modern.TNotebook",
            background="#f8fafc",
            borderwidth=0,
            tabmargins=[0, 0, 0, 0]
        )

        style.configure(
            "Modern.TNotebook.Tab",
            background="#e2e8f0",
            foreground="#475569",
            padding=[20, 12],
            font=("Segoe UI", 11, "bold"),
            borderwidth=0,
            focuscolor="none"
        )

        style.map(
            "Modern.TNotebook.Tab",
            background=[
                ("selected", "#ffffff"),
                ("active", "#f1f5f9")
            ],
            foreground=[
                ("selected", "#4f46e5"),
                ("active", "#334155")
            ]
        )

        self.configure(style="Modern.TNotebook")

class TooltipLabel(tk.Label):
    """带工具提示的标签"""

    def __init__(self, parent, tooltip_text="", **kwargs):
        super().__init__(parent, **kwargs)
        self.tooltip_text = tooltip_text
        self.tooltip_window = None

        if tooltip_text:
            self.bind("<Enter>", self.show_tooltip)
            self.bind("<Leave>", self.hide_tooltip)

    def show_tooltip(self, event):
        """显示工具提示"""
        if self.tooltip_window or not self.tooltip_text:
            return

        x = self.winfo_rootx() + 25
        y = self.winfo_rooty() + 25

        self.tooltip_window = tw = tk.Toplevel(self)
        tw.wm_overrideredirect(True)
        tw.wm_geometry(f"+{x}+{y}")

        label = tk.Label(
            tw,
            text=self.tooltip_text,
            background="#1f2937",
            foreground="white",
            font=("Segoe UI", 9),
            padx=8,
            pady=4,
            relief="solid",
            borderwidth=1
        )
        label.pack()

    def hide_tooltip(self, event):
        """隐藏工具提示"""
        if self.tooltip_window:
            self.tooltip_window.destroy()
            self.tooltip_window = None

class ProgressCard(tk.Frame):
    """进度卡片组件"""

    def __init__(self, parent, title="", current=0, total=100, **kwargs):
        super().__init__(parent, **kwargs)

        self.theme = ModernTheme()
        self.current = current
        self.total = total

        self.configure(
            bg=self.theme.COLORS["white"],
            relief="solid",
            borderwidth=1,
            padx=16,
            pady=16
        )

        # 标题
        if title:
            title_label = tk.Label(
                self,
                text=title,
                font=self.theme.FONTS["heading"],
                bg=self.theme.COLORS["white"],
                fg=self.theme.COLORS["text_primary"]
            )
            title_label.pack(anchor="w", pady=(0, 8))

        # 进度条容器
        progress_container = tk.Frame(self, bg=self.theme.COLORS["white"])
        progress_container.pack(fill=tk.X, pady=(0, 8))

        # 进度条背景
        self.progress_bg = tk.Frame(
            progress_container,
            bg=self.theme.COLORS["gray_200"],
            height=8
        )
        self.progress_bg.pack(fill=tk.X)

        # 进度条前景
        self.progress_fg = tk.Frame(
            self.progress_bg,
            bg=self.theme.COLORS["primary"],
            height=8
        )

        # 进度文本
        self.progress_text = tk.Label(
            self,
            text=f"{current}/{total}",
            font=self.theme.FONTS["small"],
            bg=self.theme.COLORS["white"],
            fg=self.theme.COLORS["text_secondary"]
        )
        self.progress_text.pack(anchor="w")

        self.update_progress()

    def update_progress(self, current=None, total=None):
        """更新进度"""
        if current is not None:
            self.current = current
        if total is not None:
            self.total = total

        # 计算进度百分比
        if self.total > 0:
            progress_percent = self.current / self.total
        else:
            progress_percent = 0

        # 更新进度条
        self.progress_fg.place(relwidth=progress_percent, relheight=1.0)

        # 更新文本
        self.progress_text.configure(text=f"{self.current}/{self.total}")

def apply_modern_theme(root):
    """应用现代主题"""
    style = ttk.Style()

    # 设置主题
    try:
        style.theme_use('clam')
    except:
        pass

    # 配置全局样式
    style.configure(".", font=("Segoe UI", 10))

    # 配置窗口背景
    root.configure(bg="#f8fafc")

    # 配置现代化的ttk样式
    theme = ModernTheme()

    # 配置Notebook样式
    style.configure(
        "TNotebook",
        background=theme.COLORS["bg_secondary"],
        borderwidth=0
    )

    style.configure(
        "TNotebook.Tab",
        background=theme.COLORS["gray_200"],
        foreground=theme.COLORS["text_secondary"],
        padding=[16, 10],
        font=theme.FONTS["default"],
        borderwidth=0,
        focuscolor="none"
    )

    style.map(
        "TNotebook.Tab",
        background=[
            ("selected", theme.COLORS["white"]),
            ("active", theme.COLORS["gray_100"])
        ],
        foreground=[
            ("selected", theme.COLORS["primary"]),
            ("active", theme.COLORS["text_primary"])
        ]
    )

    return style
